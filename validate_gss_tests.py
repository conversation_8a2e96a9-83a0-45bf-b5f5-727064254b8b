#!/usr/bin/env python3
"""
Validation script for GSS Free Fee tests.
This script validates that all test files are properly structured and can be imported.
"""

import sys
import os
import importlib.util
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def validate_test_file(test_file_path):
    """Validate a single test file."""
    print(f"\n📋 Validating {test_file_path}...")

    if not Path(test_file_path).exists():
        print(f"❌ File not found: {test_file_path}")
        return False

    try:
        # First, try to parse the file to check syntax and structure
        with open(test_file_path, 'r') as f:
            content = f.read()

        # Check for basic test structure patterns
        test_classes = []
        test_methods = []

        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('class Test') and ':' in line:
                class_name = line.split('class ')[1].split('(')[0].split(':')[0].strip()
                test_classes.append(class_name)
            elif line.startswith('def test_') and ':' in line:
                method_name = line.split('def ')[1].split('(')[0].strip()
                test_methods.append(method_name)

        print(f"✅ File structure validated")
        print(f"   📊 Test classes: {len(test_classes)}")
        print(f"   🧪 Test methods: {len(test_methods)}")

        if test_classes:
            print(f"   📝 Classes: {', '.join(test_classes)}")

        # Try to compile the file
        compile(content, test_file_path, 'exec')
        print(f"   ✅ Syntax validation passed")

        # Try to import if dependencies are available
        try:
            spec = importlib.util.spec_from_file_location("test_module", test_file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            print(f"   ✅ Import validation passed")
        except ImportError as e:
            if 'pyspark' in str(e).lower():
                print(f"   ⚠️  Import skipped (PySpark not available): {str(e)}")
            else:
                print(f"   ❌ Import failed: {str(e)}")
                return False
        except Exception as e:
            print(f"   ❌ Import failed: {str(e)}")
            return False

        return True

    except SyntaxError as e:
        print(f"❌ Syntax error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Validation failed: {str(e)}")
        return False

def validate_main_job():
    """Validate that the main job file can be imported."""
    print(f"\n📋 Validating main job file...")

    job_file = "src/jobs/calculations/gss_free_fee.py"
    if not Path(job_file).exists():
        print(f"❌ Job file not found: {job_file}")
        return False

    try:
        # First check file structure
        with open(job_file, 'r') as f:
            content = f.read()

        # Check for class definition
        if 'class GSSFreeFee:' not in content:
            print(f"❌ GSSFreeFee class not found in file")
            return False

        # Check for expected methods
        expected_methods = [
            '__init__',
            'load_gss_transaction_base',
            'get_first_txn_users_last_30_days',
            'find_gss_transaction',
            'find_eligible_user',
            'build_mongo_config',
            'start_processing',
            'run'
        ]

        missing_methods = []
        for method in expected_methods:
            if f'def {method}(' not in content:
                missing_methods.append(method)

        if missing_methods:
            print(f"❌ Missing methods: {', '.join(missing_methods)}")
            return False
        else:
            print(f"✅ All expected methods found ({len(expected_methods)} methods)")

        # Try to compile the file
        compile(content, job_file, 'exec')
        print(f"✅ Syntax validation passed")

        # Try to import if dependencies are available
        try:
            from src.jobs.calculations.gss_free_fee import GSSFreeFee
            print(f"✅ Successfully imported GSSFreeFee class")
            return True
        except ImportError as e:
            if 'pyspark' in str(e).lower():
                print(f"⚠️  Import skipped (PySpark not available): {str(e)}")
                print(f"✅ File structure validation passed")
                return True
            else:
                print(f"❌ Import failed: {str(e)}")
                return False

    except Exception as e:
        print(f"❌ Validation failed: {str(e)}")
        return False

def validate_dependencies():
    """Validate that required dependencies can be imported."""
    print(f"\n📋 Validating dependencies...")

    dependencies = [
        ('pytest', None),  # pytest module itself
        ('pyspark.sql', 'SparkSession'),
        ('unittest.mock', 'Mock'),
        ('datetime', 'datetime')
    ]

    success = True
    missing_deps = []

    for module_name, class_name in dependencies:
        try:
            if class_name:
                module = __import__(module_name, fromlist=[class_name])
                getattr(module, class_name)
                print(f"✅ {module_name}.{class_name}")
            else:
                __import__(module_name)
                print(f"✅ {module_name}")
        except ImportError as e:
            print(f"⚠️  {module_name}{('.' + class_name) if class_name else ''}: {str(e)}")
            missing_deps.append(module_name)
            if module_name in ['pyspark.sql']:
                # PySpark is optional for structure validation
                print(f"   ℹ️  {module_name} is optional for test structure validation")
            else:
                success = False
        except AttributeError as e:
            print(f"❌ {module_name}.{class_name}: {str(e)}")
            success = False

    if missing_deps:
        print(f"\n📝 Missing dependencies: {', '.join(missing_deps)}")
        print("   To install missing dependencies:")
        if 'pytest' in missing_deps:
            print("   pip install pytest pytest-cov")
        if 'pyspark.sql' in missing_deps:
            print("   pip install pyspark")

    return success

def main():
    """Main validation function."""
    print("🔍 GSS Free Fee Test Suite Validation")
    print("=" * 50)
    
    # Test files to validate
    test_files = [
        "tests/test_gss_free_fee.py",
        "tests/test_gss_free_fee_integration.py",
        "tests/test_gss_free_fee_data_validation.py", 
        "tests/test_gss_free_fee_no_spark.py"
    ]
    
    success = True
    
    # Validate main job
    if not validate_main_job():
        success = False
    
    # Validate dependencies
    if not validate_dependencies():
        success = False
    
    # Validate each test file
    for test_file in test_files:
        if not validate_test_file(test_file):
            success = False
    
    # Summary
    print(f"\n{'=' * 50}")
    if success:
        print("🎉 All validations passed!")
        print("✅ Test suite is ready to run")
        print("\nNext steps:")
        print("1. Run tests: python3 run_gss_free_fee_tests.py")
        print("2. Run with coverage: python3 run_gss_free_fee_tests.py --coverage")
        print("3. Run specific category: python3 run_gss_free_fee_tests.py --unit-only")
    else:
        print("❌ Some validations failed")
        print("Please fix the issues above before running tests")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
