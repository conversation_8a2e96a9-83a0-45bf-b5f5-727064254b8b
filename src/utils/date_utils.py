from datetime import date, timedelta, datetime, timezone
from dateutil.relativedelta import relativedelta
import pytz
import calendar


class DateUtils:
    @staticmethod
    def get_jkt_date(offset):
        zone = pytz.timezone("Asia/Jakarta")
        return (datetime.now(tz=zone) - timedelta(offset)).date()

    @staticmethod
    def get_utc_date(offset):
        zone = pytz.timezone("UTC")
        return (datetime.now(tz=zone) - timedelta(offset)).date()

    @staticmethod
    def get_utc_timestamp():
        zone = pytz.timezone("UTC")
        return datetime.now(tz=zone)

    @staticmethod
    def deduct_months(dt: datetime, months):
        return dt - relativedelta(months=months)

    @staticmethod
    def is_last_day_of_month(dt: datetime):
        last_day = calendar.monthrange(dt.year, dt.month)[1]
        return dt.day == last_day

    @staticmethod
    def get_cutoff_time_and_jkt_date(config):
        cutoff_ts = datetime.now(tz=pytz.timezone("UTC"))

        if config["execution_time"] == "gss_market_close":
            cutoff_ts = (cutoff_ts.replace(hour=0, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)
        elif config["execution_time"] == "gss_post_market_close":
            if cutoff_ts.hour >= 2 or (cutoff_ts.hour == 1 and cutoff_ts.minute >= 30):
                cutoff_ts = (cutoff_ts.replace(hour=1, minute=30, second=0, microsecond=0)) - timedelta(microseconds=1)
            else:
                cutoff_ts = ((cutoff_ts - timedelta(days=1)).replace(hour=1, minute=30, second=0, microsecond=0)) - timedelta(microseconds=1)
        elif config["execution_time"] == "8_pm_est":
            eastern = pytz.timezone("US/Eastern")
            utc = pytz.utc

            now_est = datetime.now(tz=utc).astimezone(eastern)

            if now_est.hour >= 20:
                naive_est_time = now_est.replace(hour=20, minute=0, second=0, microsecond=0).replace(tzinfo=None)
            else:
                yesterday = now_est - timedelta(days=1)
                naive_est_time = yesterday.replace(hour=20, minute=0, second=0, microsecond=0).replace(tzinfo=None)

            if config["offset"] != 0:
                naive_est_time = naive_est_time - timedelta(days=config["offset"])

            est_time = eastern.localize(naive_est_time)

            cutoff_ts = est_time.astimezone(utc)
        elif config["execution_time"] == "jkt_day_end":
            if cutoff_ts.hour >= 17:
                cutoff_ts = (cutoff_ts.replace(hour=17, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)
            else:
                cutoff_ts = ((cutoff_ts - timedelta(days=1)).replace(hour=17, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)
        elif config["execution_time"] == "8_am_jkt":
            if cutoff_ts.hour >= 1:
                cutoff_ts = (cutoff_ts.replace(hour=1, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)
            else:
                cutoff_ts = ((cutoff_ts - timedelta(days=1)).replace(hour=1, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)
        elif config["execution_time"] == "9_am_jkt":
            if cutoff_ts.hour >= 2:
                cutoff_ts = (cutoff_ts.replace(hour=2, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)
            else:
                cutoff_ts = ((cutoff_ts - timedelta(days=1)).replace(hour=2, minute=0, second=0, microsecond=0)) - timedelta(microseconds=1)


        if (config["offset"] != 0) and (config["execution_time"] != "8_pm_est"):
            cutoff_ts = cutoff_ts - timedelta(days=config["offset"])

        cutoff_ts_jkt = cutoff_ts.astimezone(pytz.timezone("Asia/Jakarta"))
        t_1 = cutoff_ts_jkt.date()
        t_2 = t_1 - timedelta(days=1)
        return cutoff_ts, t_1, t_2

    @staticmethod
    def get_jkt_timestamp(offset=0):
        zone = pytz.timezone("Asia/Jakarta")
        return datetime.now(tz=zone) - timedelta(offset)

    @staticmethod
    def get_tc_dates_and_timestamp(utc_datetime, config_data):
        """
        Calculates trading competition date parameters based on UTC datetime.
        Returns:
            t_1: Current date in Jakarta timezone
            h_1: Current hour in Jakarta timezone
            t_2: Previous date (based on trading_competition_frequency)
            h_2: Hour from previous date
            ts_1: Formatted current UTC timestamp (ISO format)
            ts_2: Formatted UTC timestamp for 17:00 (ISO format) of either the current day or previous day (if current time is after 5 PM)
        """
        # Localize the datetime object to UTC
        utc_datetime = pytz.utc.localize(utc_datetime)

        # Convert the UTC datetime to Jakarta timezone
        jkt_datetime = utc_datetime.astimezone(pytz.timezone("Asia/Jakarta"))

        # Get the t1 and t2 hour in Jakarta timezone
        t_1 = jkt_datetime.date()
        h_1 = jkt_datetime.hour
        t_2 = (jkt_datetime - timedelta(hours=config_data["trading_competition"]["frequency"])).date()
        h_2 = (jkt_datetime - timedelta(hours=config_data["trading_competition"]["frequency"])).hour
        if (jkt_datetime - timedelta(hours=config_data["trading_competition"]["frequency"])).hour == 0:
            t_2 = (jkt_datetime - timedelta(hours=config_data["trading_competition"]["frequency"]) - timedelta(
                seconds=1)).date()
            h_2 = 24
        if jkt_datetime.hour == 0:
            t_1 = (jkt_datetime - timedelta(seconds=1)).date()
            h_1 = 24
        ts_1 = utc_datetime.replace(minute=0, second=0, microsecond=0)
        ts_2 = utc_datetime.replace(hour=17, minute=0, second=0, microsecond=0)
        if ts_2 >= ts_1:
            ts_2 = ts_2 - timedelta(days=1)

        ts_1 = ts_1.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        ts_2 = ts_2.strftime("%Y-%m-%dT%H:%M:%S.000Z")
        return t_1, h_1, t_2, h_2, ts_1, ts_2

    @staticmethod
    def get_utc_timestamp_from_string(ts):
        format_str = "%Y-%m-%d %H:%M:%S.%f"
        try:
            return datetime.strptime(ts, format_str)
        except ValueError as e:
            print(f"Timestamp parsing failed for input '{ts}': {e}")
            return None

    @staticmethod
    def dates_between(start_date_str: str, end_date_str: str):
        """
        Inclusive list of YYYY-MM-DD strings between start and end.
        """
        start = datetime.strptime(start_date_str, "%Y-%m-%d").date()
        end = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        if end < start:
            start, end = end, start
        days = (end - start).days
        return [(start + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(days + 1)]

    @staticmethod
    def get_tax_year_params(t_1: date):
        if (t_1.month == 12) and (t_1.day == 31):
            tax_year = t_1.year
            tax_period_end_date = t_1
            tax_period_start_date = t_1.replace(year=tax_year - 1, month=12, day=31)
        else:
            tax_year = t_1.year - 1
            tax_period_end_date = t_1.replace(year=tax_year, month=12, day=31)
            tax_period_start_date = t_1.replace(year=tax_year - 1, month=12, day=31)
        return tax_year, tax_period_end_date, tax_period_start_date
