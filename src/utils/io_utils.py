from src.utils.spark_utils import *
from src.utils.date_utils import *
from src.utils.python_utils import PythonUtils
from datetime import datetime, timedelta
from redis import Redis
import json
 

class IOUtils:
    def __init__(self, spark: SparkSession, config):
        self.spark = spark
        self.config = config
        self.logger = get_logger()
        self.ops = Operations(self.spark)

    # Convenience alias used by some jobs
    def read_parquet(self, path, schema=None, return_empty_if_file_not_present=False):
        return self.read_parquet_data(path, schema=schema, return_empty_if_file_not_present=return_empty_if_file_not_present)

    def check_if_file_exist_in_hdfs(self, path):
        sc = self.spark.sparkContext
        hadoop_conf = sc._jsc.hadoopConfiguration()
        java_import(sc._gateway.jvm, "org.apache.hadoop.fs.FileSystem")
        java_import(sc._gateway.jvm, "org.apache.hadoop.fs.Path")
        fs = sc._gateway.jvm.FileSystem.get(hadoop_conf)
        path = sc._gateway.jvm.Path(path)
        if fs.exists(path):
            return True
        else:
            return False

    def read_deleted_records(self, path):
        upperbound_ts = datetime.now()
        lowerbound_ts = upperbound_ts - timedelta(days=2)
        try:
            df = self.spark.read.option("recursiveFileLookup", "true").json(path,
                                                        modifiedAfter=lowerbound_ts.strftime("%Y-%m-%dT%H:%M:%S"),
                                                            modifiedBefore=upperbound_ts.strftime("%Y-%m-%dT%H:%M:%S"))

            delete_record = df.select("value.*").filter(col("__op") == "d").select("id").distinct().rdd.flatMap(lambda x:x).collect()
        except Exception as e:
            delete_record = []
        return delete_record

    def read_csv_file(self, path, schema=None, return_empty=False, use_escape=True, alert_if_file_not_present=True):
        hdfs_path = path.replace("s3a:", "hdfs:/").replace("*.csv", "")
        if self.check_if_file_exist_in_hdfs(hdfs_path):
            path = hdfs_path
        try:
            if schema is not None and use_escape:
                df = self.spark.read.option("recursiveFileLookup", "true").csv(path, header=True, inferSchema=True,
                                    quote='"', escape='"', multiLine=True, schema=schema)
            elif schema is None and use_escape:
                df = self.spark.read.option("recursiveFileLookup", "true").csv(path, header=True, inferSchema=True,
                                     quote='"', escape='"', multiLine=True)
            elif schema is not None and not use_escape:
                df = self.spark.read.option("recursiveFileLookup", "true").csv(path, header=True, inferSchema=True,
                                    schema=schema)
            else:
                df = self.spark.read.option("recursiveFileLookup", "true").csv(path, header=True, inferSchema=True)
            return df
        except Exception as e:
            if return_empty:
                if alert_if_file_not_present:
                    self.logger.warning("reading from path {} given exception : {}, returning empty dataframe".format(path, repr(e)))
                df = None
                if schema is not None:
                    empty_rdd = self.spark.sparkContext.emptyRDD()
                    df = self.spark.createDataFrame(empty_rdd, schema)
                return df
            else:
                self.logger.error("reading from path {} given exception: {}".format(path, repr(e)))
                raise

    def read_json_data(self, path, is_raw=False, schema=None, return_empty_if_file_not_present=False, alert_file_not_present=True):
        try:
            df = self.spark.read.option("recursiveFileLookup", "true").json(path)
            if is_raw:
                df = df.select("value.*").drop("__deleted", "__lsn", "__op", "__source_ts_ms", "__table")
            if schema is not None:
                cols = schema.fieldNames()
                df = df.select(cols)
                data_types = schema.fields
                for data_type in data_types:
                    df = df.withColumn(data_type.name, col(data_type.name).cast(data_type.dataType))
            return df
        except AnalysisException as ae:
            if "Path does not exist:" in str(ae):
                if return_empty_if_file_not_present is False:
                    self.logger.error("An error occurred: {}".format(repr(ae)))
                    raise ae
                elif schema is not None:
                    empty_rdd = self.spark.sparkContext.emptyRDD()
                    df = self.spark.createDataFrame(empty_rdd, schema)
                    if not is_raw and alert_file_not_present:
                        self.logger.warning("Path {} doesn't exist, returning empty dataset".format(path))
                    return df
                else:
                    if not is_raw and alert_file_not_present:
                        self.logger.warning("Path {} doesn't exist, returning None".format(path))
                    return None
            else:
                self.logger.error("An error occurred: {}".format(repr(ae)))
                raise ae
        except Exception as e:
            self.logger.error("An error occurred: {}".format(repr(e)))
            raise e

    def read_parquet_data(self, path, schema=None, return_empty_if_file_not_present=False):
        try:
            df = self.spark.read.option("recursiveFileLookup", "true").parquet(path)
            if schema is not None:
                cols = schema.fieldNames()
                df = df.select(cols)
                data_types = schema.fields
                for data_type in data_types:
                    df = df.withColumn(data_type.name, col(data_type.name).cast(data_type.dataType))
            return df
        except AnalysisException as ae:
            error_message = str(ae)
            if "Path does not exist:" in error_message or "Unable to infer schema" in error_message:
                if return_empty_if_file_not_present is False:
                    raise ae
                elif schema is not None:
                    empty_rdd = self.spark.sparkContext.emptyRDD()
                    df = self.spark.createDataFrame(empty_rdd, schema)
                    return df
                else:
                    return None
            else:
                raise ae
        except Exception as e:
            raise e

    def write_json_file(self, df: DataFrame, path, partition=1, mode="overwrite"):
        df = df.coalesce(partition)
        df.write.mode(mode).json(path)

    def write_csv_file(self, df: DataFrame, path, partition=1, mode="overwrite"):
        df = df.coalesce(partition)
        df.write.mode(mode).csv(path, header=True)

    def write_parquet_file(self, df: DataFrame, path, partition=1, mode="overwrite"):
        df = df.coalesce(partition)
        df.write.mode(mode).parquet(path)

    

    def write_data_in_kafka(self, df, topic):
        df.write.format("kafka") \
            .option("kafka.security.protocol", self.config["kafka_security_protocol"]) \
            .option("kafka.ssl.truststore.location", self.config["kafka_truststore_location"]) \
            .option("kafka.ssl.truststore.password", self.config["kafka_truststore_password"]) \
            .option("kafka.ssl.keystore.location", self.config["kafka_keystore_location"]) \
            .option("kafka.ssl.keystore.password", self.config["kafka_keystore_password"]) \
            .option("kafka.ssl.key.password", self.config["kafka_key_password"]) \
            .option("kafka.ssl.keystore.type", self.config["kafka_keystore_type"]) \
            .option("kafka.bootstrap.servers", self.config["bootstrap_servers"]) \
            .option("topic", topic) \
            .save()

    def transform_column_name(self, col):
        if col == "created":
            return col + "_at"
        return col

    def write_dataset_to_mongo(self, df, mongo_config, asset_name, write_format, shardkey,
                                     add_created_at: bool = True):
        try:
            if add_created_at:
                current_timestamp = DateUtils.get_jkt_timestamp(self.config["offset"]).date()
                df = df.withColumn("created", lit(current_timestamp))

            original_columns = df.columns

            for col in original_columns:
                transformed_col_name = self.transform_column_name(col)
                renamed_col = PythonUtils.convert_snake_to_camel_case(transformed_col_name)
                df = df.withColumnRenamed(col, renamed_col)

            mongo_options = {
                "uri": mongo_config["uri"],
                "batchsize": mongo_config["batch_size"]
            }
            if write_format == "update":
                mongo_options.update({
                    "replaceDocument": "false",
                    "shardKey": shardkey
                })
            df.write.format("mongo").options(**mongo_options).mode(mongo_config["mode"]).save()
        except Exception as e:
            self.logger.error(f"error message {e}", exc_info=True)
            raise e

    def ssl_kafka_config(self, kafka_obj):
        return kafka_obj.option("kafka.security.protocol", self.config["kafka_security_protocol"]) \
            .option("kafka.ssl.truststore.location", self.config["kafka_truststore_location"]) \
            .option("kafka.ssl.truststore.password", self.config["kafka_truststore_password"]) \
            .option("kafka.ssl.keystore.location", self.config["kafka_keystore_location"]) \
            .option("kafka.ssl.keystore.password", self.config["kafka_keystore_password"]) \
            .option("kafka.ssl.key.password", self.config["kafka_key_password"]) \
            .option("kafka.ssl.keystore.type", self.config["kafka_keystore_type"])

    def read_from_kafka_in_memory(self, bootstrap_servers, topics):
        try:
            self.logger.info("reading data from {} kafka topic/topics".format(topics))
            df = self.spark.read.format("kafka") \
                .option("kafka.bootstrap.servers", bootstrap_servers) \
                .option("subscribe", topics)
            if self.config["kafka_security_protocol"] == "SSL":
                df = self.ssl_kafka_config(df)
            df = df.load()
            df = df.selectExpr("CAST(value AS STRING)")
            df = df.withColumn("value", from_json(df.value, MapType(StringType(), StringType())))
            self.logger.info("Successfully read {} data".format(topics))
            return df
        except Exception as e:
            self.logger.error("Error reading from Kafka topic {}: {}".format(topics, repr(e)))
            self.logger.exception(e)
            raise e

    def read_from_postgres(self, pg_config, db_table, port=5432):
        try:
            db_name, table_name = db_table.split(".", 1)
            url = f"jdbc:postgresql://{pg_config['host']}:{port}/{db_name}"
            df = self.spark.read.format("jdbc") \
                .option("user", pg_config['username']) \
                .option("password", pg_config['password']) \
                .option("url", url) \
                .option("dbtable", table_name) \
                .load()
            return df
        except Exception as e:
            self.logger.error(f"Error reading from JDBC source: {repr(e)}")
            raise e

    def read_from_mongodb(self, mongo_config, collection, pipeline=None):
        if not pipeline:
            msg = "No pipeline provided. Skipping MongoDB read to avoid loading the entire collection."
            self.logger.error(msg)
            raise ValueError(msg)
        try:
            mongo_config["collection"] = collection
            mongo_uri = self.get_mongo_connection_string(mongo_config)
            reader = self.spark.read.format("com.mongodb.spark.sql.DefaultSource") \
                .option("spark.mongodb.input.uri", mongo_uri)
            if pipeline:
                reader = reader.option("pipeline", pipeline)
            df = reader.load()
            return df
        except Exception as e:
            self.logger.error(f"Error reading from MongoDB: {repr(e)}")
            raise e

    def read_from_bigquery(self, database_name, query):
        try:
            self.logger.info(f"Reading data from BigQuery with query: {query}")
            credentials_path = self.config["bq_service_account_file_path"]
            parentProject = self.config["bq_project"]
            df = self.spark.read.format("bigquery") \
                        .option("viewsEnabled", "true") \
                        .option("credentialsFile", credentials_path) \
                        .option("parentProject", parentProject) \
                        .option("materializationDataset", database_name) \
                        .option("query", query) \
                        .load()
            self.logger.info("Successfully read data from BigQuery")
            return df
        except Exception as e:
            self.logger.error(f"Error reading from BigQuery: {repr(e)}")
            raise e
    
    def get_asset_data(self, asset, source, schema=None):
        if source is None or source == "kafka":
            self.logger.info("reading asset data from kafka topic")
            kafka_topics = self.config.get("kafka_topics", {})
            if not kafka_topics:
                self.logger.error("kafka_topics not found in configuration")
            asset_data = self.read_from_kafka_in_memory(self.config["bootstrap_servers"], self.config["kafka_topics"][asset])
            asset_data = self.ops.explode_col_df(asset_data)
            asset_data = self.ops.de_dupe_dataframe(asset_data, ["id"], "__source_ts_ms")
            self.logger.info("successfully read asset data from kafka")
        else:
            return None
        return asset_data

    def get_mongo_connection_string(self, mongo_config):
        return "mongodb+srv://{}:{}@{}/{}".format(mongo_config["username"],
                                                     mongo_config["password"],
                                                     mongo_config["host"],
                                                     mongo_config["collection"])

