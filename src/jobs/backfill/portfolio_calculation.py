from src.utils.spark_utils import *
from datetime import date, timedelta, datetime, timezone

class PortfolioCalculation:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("portfolio_calculation")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config["t_2"]
        self.start_dt = kwargs.get("start_dt")
        self.end_dt = kwargs.get("end_dt")
        self.pluang_partner_id = self.config["pluang_partner_id"]

    def get_crypto_currency_snapshot(self):
        crypto_currency_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.crypto_currency_snapshot, self.t_1)
        )
        crypto_currency_snap = crypto_currency_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("total_reward_value", col("total_reward_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))

        crypto_currency_snap = crypto_currency_snap\
            .withColumn("total_cost", (col("total_quantity") * col("weighted_cost")))

        crypto_currency_snap = crypto_currency_snap.groupBy("account_id").agg(
            sum("total_value").alias("crypto_total_value"),
            sum("total_reward_value").alias("crypto_total_reward_value"),
            sum("realised_gain").alias("crypto_realised_gain"),
            sum("unrealised_gain").alias("crypto_unrealised_gain"),
            sum("total_cost").alias("crypto_total_cost")
        )
        return crypto_currency_snap

    def get_forex_snapshot(self):
        forex_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.forex_snapshot, self.t_1)
        )

        forex_snap = forex_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))

        forex_snap = forex_snap.withColumn("total_cost", (col("total_quantity") * col("weighted_cost")))

        forex_snap = forex_snap.groupBy("account_id").agg(
            sum("total_value").alias("forex_total_value"),
            sum("total_quantity").alias("forex_total_quantity"),
            sum("realised_gain").alias("forex_realised_gain"),
            sum("unrealised_gain").alias("forex_unrealised_gain"),
            sum("total_cost").alias("forex_total_cost")
        )

        return forex_snap

    def get_crypto_currency_pocket_snapshot(self):
        crypto_currency_pocket_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.crypto_currency_pocket_snapshot, self.t_1)
        )
        crypto_currency_pocket_snap = crypto_currency_pocket_snap\
            .withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))

        crypto_currency_pocket_snap = crypto_currency_pocket_snap\
            .withColumn("total_cost",(col("total_quantity")*col("weighted_cost")))

        crypto_currency_pocket_snap = crypto_currency_pocket_snap.groupBy("account_id").agg(
            sum("total_value").alias("crypto_pocket_total_value"),
            sum("realised_gain").alias("crypto_pocket_realised_gain"),
            sum("unrealised_gain").alias("crypto_pocket_unrealised_gain"),
            sum("total_cost").alias("crypto_pocket_total_cost")
        )

        return crypto_currency_pocket_snap

    def get_fund_snapshot(self):
        fund_snap = self.io_utils.read_csv_file("{}/{}/dt={}".format(self.bucket_path, S3Paths.fund_snapshot, self.t_1))
        fund_snap = fund_snap.withColumn("total_value", col("total_value").cast(DoubleType())).withColumn("realised_gain", col("realised_gain").cast(DoubleType())).withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType())).withColumn("realised_gain_idr", col("realised_gain_idr").cast(DoubleType())).withColumn("unrealised_gain_idr", col("unrealised_gain_idr").cast(DoubleType()))
        fund_snap = fund_snap.withColumn("total_cost",(col("total_quantity")*col("weighted_cost")))
        fund_usd_snap = fund_snap.where(fund_snap.currency == "USD")
        fund_snap = fund_snap.where(fund_snap.currency == "IDR")
        fund_snap = fund_snap.groupBy("account_id").agg(sum("total_value_idr").alias("fund_total_value"),sum("realised_gain").alias("fund_realised_gain"),sum("unrealised_gain").alias("fund_unrealised_gain"),sum("total_cost").alias("fund_total_cost"))
        fund_usd_snap = fund_usd_snap.groupBy("account_id").agg(sum("total_value_idr").alias("fund_usd_total_value_idr"),sum("total_value").alias("fund_usd_total_value"),sum("realised_gain").alias("fund_usd_realised_gain"),sum("unrealised_gain").alias("fund_usd_unrealised_gain"),sum("realised_gain_idr").alias("fund_usd_realised_gain_idr"),sum("unrealised_gain_idr").alias("fund_usd_unrealised_gain_idr"),sum("total_cost").alias("fund_usd_total_cost"))
        return fund_snap, fund_usd_snap

    def get_gold_snapshot(self):
        gold_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.gold_snapshot, self.t_1)
        )
        self.logger.info("Filter tokopedia users from gold")
        gold_snap = gold_snap.filter(col("partner_id") == self.pluang_partner_id)

        gold_snap = gold_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))

        gold_snap = gold_snap.withColumn("total_cost", (col("total_quantity") * col("weighted_cost")))

        gold_snap = gold_snap.groupBy("account_id").agg(
            sum("total_value").alias("gold_total_value"),
            sum("realised_gain").alias("gold_realised_gain"),
            sum("unrealised_gain").alias("gold_unrealised_gain"),
            sum("total_cost").alias("gold_total_cost")
        )
        return gold_snap

    def get_stock_index_snapshot(self):
        stock_index_snap = self.io_utils.read_csv_file(
            "{}/{}/".format(self.bucket_path, S3Paths.stock_index_snapshot)
        )
        stock_index_snap = stock_index_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))\
            .withColumn("realised_gain_idr", col("realised_gain_idr").cast(DoubleType()))\
            .withColumn("unrealised_gain_idr", col("unrealised_gain_idr").cast(DoubleType()))

        stock_index_snap = stock_index_snap.withColumn("total_cost", (col("total_quantity")*col("weighted_cost_idr")))

        stock_index_snap = stock_index_snap.groupBy("account_id").agg(
            sum("total_value").alias("stock_total_value"),
            sum("total_value_idr").alias("stock_total_value_idr"),
            sum("realised_gain").alias("stock_index_realised_gain"),
            sum("unrealised_gain").alias("stock_index_unrealised_gain"),
            sum("realised_gain_idr").alias("stock_index_realised_gain_idr"),
            sum("unrealised_gain_idr").alias("stock_index_unrealised_gain_idr"),
            sum("total_cost").alias("stock_index_total_cost")
        )
        return stock_index_snap

    def get_global_stock_snapshot(self):
        global_stock_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.global_stock_snapshot, self.t_1)
        )
        global_stock_snap = global_stock_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("total_reward_value", col("total_reward_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))\
            .withColumn("realised_gain_idr", col("realised_gain_idr").cast(DoubleType()))\
            .withColumn("unrealised_gain_idr", col("unrealised_gain_idr").cast(DoubleType()))

        global_stock_snap = global_stock_snap.withColumn("total_cost",
            when(col("is_cfd_leverage") == True, 0).otherwise(col("total_quantity")*col("weighted_cost_idr")))
        global_stock_snap = global_stock_snap.withColumn("net_total_value",
            when(col("is_cfd_leverage") == True, col("unrealised_gain")).otherwise(col("total_value")))
        global_stock_snap = global_stock_snap.withColumn("net_total_value_idr",
            when(col("is_cfd_leverage") == True, col("unrealised_gain_idr")).otherwise(col("total_value_idr")))

        global_stock_snap = global_stock_snap.groupBy("account_id").agg(
            sum("net_total_value").alias("net_global_stock_total_value"),
            sum("total_reward_value").alias("global_stock_total_reward_value"),
            sum("net_total_value_idr").alias("net_global_stock_total_value_idr"),
            sum("total_dividend").alias("global_stock_total_dividend"),
            sum("total_dividend_idr").alias("global_stock_total_dividend_idr"),
            sum("realised_gain").alias("global_stock_realised_gain"),
            sum("unrealised_gain").alias("global_stock_unrealised_gain"),
            sum("realised_gain_idr").alias("global_stock_realised_gain_idr"),
            sum("unrealised_gain_idr").alias("global_stock_unrealised_gain_idr"),
            sum("total_cost").alias("global_stock_total_cost")
        )
        return global_stock_snap

    def get_global_stock_option_accounts_snapshot(self):
        options_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.options_snapshot, self.t_1)
        )
        options_snap = options_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))\
            .withColumn("realised_gain_idr", col("realised_gain_idr").cast(DoubleType()))\
            .withColumn("unrealised_gain_idr", col("unrealised_gain_idr").cast(DoubleType()))

        options_snap = options_snap.withColumn("total_cost", (col("total_value") - col("unrealised_gain_idr")))
        options_snap = options_snap.groupBy("account_id").agg(
            sum("total_value").alias("options_total_value"),
            sum("total_value_idr").alias("options_total_value_idr"),
            sum("realised_gain").alias("options_realised_gain"),
            sum("unrealised_gain").alias("options_unrealised_gain"),
            sum("realised_gain_idr").alias("options_realised_gain_idr"),
            sum("unrealised_gain_idr").alias("options_unrealised_gain_idr"),
            sum("total_cost").alias("options_total_cost")
        )
        return options_snap

    def get_crypto_future_positions_snapshot(self):
        futures_position_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.crypto_futures_snapshot, self.t_1)
        )
        futures_position_snap = futures_position_snap.groupBy("account_id").agg(
            sum("realised_gain").alias("crypto_futures_realised_gain")
        )
        return futures_position_snap

    def get_global_stock_pocket_snapshot(self):
        global_stock_pocket_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.global_stock_pocket_snapshot, self.t_1)
        )

        global_stock_pocket_snap = global_stock_pocket_snap\
            .withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))\
            .withColumn("realised_gain_idr", col("realised_gain_idr").cast(DoubleType()))\
            .withColumn("unrealised_gain_idr", col("unrealised_gain_idr").cast(DoubleType()))

        global_stock_pocket_snap = global_stock_pocket_snap\
            .withColumn("total_cost", (col("total_quantity") * col("weighted_cost_idr")))

        global_stock_pocket_snap = global_stock_pocket_snap.groupBy("account_id").agg(
            sum("total_value").alias("global_stock_pocket_total_value"),
            sum("total_value_idr").alias("global_stock_pocket_total_value_idr"),
            sum("total_dividend").alias("global_stock_pocket_total_dividend"),
            sum("total_dividend_idr").alias("global_stock_pocket_total_dividend_idr"),
            sum("realised_gain").alias("global_stock_pocket_realised_gain"),
            sum("unrealised_gain").alias("global_stock_pocket_unrealised_gain"),
            sum("realised_gain_idr").alias("global_stock_pocket_realised_gain_idr"),
            sum("unrealised_gain_idr").alias("global_stock_pocket_unrealised_gain_idr"),
            sum("total_cost").alias("global_stock_pocket_total_cost")
        )

        return global_stock_pocket_snap

    def get_wallet_snapshot(self):
        wallet_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.bappebti_wallet_snapshot, self.t_1)
        )

        wallet_snap = wallet_snap.withColumn("cash_balance", col("cash_balance").cast(DoubleType())) \
            .withColumn("voucher_balance", col("voucher_balance").cast(DoubleType())) \
            .withColumn("free_balance", col("free_balance").cast(DoubleType())) \
            .withColumn("cashback", col("cashback").cast(DoubleType())) \
            .withColumn("blocked_cash_balance", col("blocked_cash_balance").cast(DoubleType())) \
            .withColumn("cash_reward", col("cash_reward").cast(DoubleType()))

        wallet_snap = wallet_snap.filter(
            (col("cash_balance") > 0) | (col("voucher_balance") > 0)
            | (col("free_balance") > 0) | (col("cashback") > 0)
            | (col("cash_reward") > 0) | (col("blocked_cash_balance") > 0))

        wallet_snap = wallet_snap.groupBy("account_id").agg(
            sum("cash_balance").alias("cash_balance"),
            sum("voucher_balance").alias("voucher_balance"),
            sum("free_balance").alias("free_balance"),
            sum("cashback").alias("cashback"),
            sum("blocked_cash_balance").alias("blocked_cash_balance"),
            sum("cash_reward").alias("cash_reward"))

        return wallet_snap

    def get_gold_gift_and_withdrawal_snapshot(self):
        gold_gift_and_withdrawal_snapshot = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.gold_gift_and_withdrawal_snapshot, self.t_1)
        )
        gold_gift_and_withdrawal_snapshot = gold_gift_and_withdrawal_snapshot.select(["account_id", "cashDiffValue"])
        gold_gift_and_withdrawal_snapshot = gold_gift_and_withdrawal_snapshot.withColumn("cashDiffValue",
                    col("cashDiffValue").cast(DoubleType()))
        return gold_gift_and_withdrawal_snapshot

    def get_leverage_wallet_accounts_snapshot(self):
        leverage_wallet_accounts_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.leverage_wallet_accounts_snapshot, self.t_1)
        )
        leverage_wallet_accounts_snap = leverage_wallet_accounts_snap.select("account_id", "balance", "currency_to_idr",
                    "weighted_cost", "locked_margin", "trading_margin", "equity"
        )

        leverage_wallet_accounts_snap = leverage_wallet_accounts_snap.withColumn("withdrawable_balance",
                            F.least(col("equity"), col("balance")) - col("locked_margin") - col("trading_margin"))

        leverage_wallet_accounts_snap = leverage_wallet_accounts_snap.withColumnRenamed("balance", "total_quantity") \
            .withColumn("total_quantity", col("total_quantity").cast(DoubleType())) \
            .withColumn("total_value", (col("total_quantity")*col("currency_to_idr")).cast(LongType())) \
            .withColumn("total_cost", (col("total_quantity") * col("weighted_cost")).cast(LongType())) \
            .withColumn("unrealised_gain", (
                col("withdrawable_balance") * (col("currency_to_idr") - col("weighted_cost"))).cast(LongType())) \
            .drop("currency_to_idr").drop("weighted_cost")

        leverage_wallet_accounts_snap = leverage_wallet_accounts_snap.groupBy("account_id").agg(
            sum("total_quantity").alias("leverage_wallet_total_quantity"),
            sum("total_value").alias("leverage_wallet_total_value"),
            sum("total_cost").alias("leverage_wallet_total_cost"),
            sum("unrealised_gain").alias("leverage_wallet_unrealised_gain"))

        return leverage_wallet_accounts_snap

    def get_intraday_snapshot(self):
        intraday_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.global_stock_intraday_snapshot, self.t_1)
        )
        intraday_snap = intraday_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))\
            .withColumn("realised_gain_in_idr", col("realised_gain_in_idr").cast(DoubleType()))\
            .withColumn("unrealised_gain_idr", col("unrealised_gain_idr").cast(DoubleType()))

        intraday_snap = intraday_snap.withColumn("net_total_value", col("unrealised_gain"))
        intraday_snap = intraday_snap.withColumn("net_total_value_idr", col("unrealised_gain_idr"))
        intraday_snap = intraday_snap.groupBy("account_id").agg(
            sum("net_total_value").alias("net_intraday_total_value"),
            sum("net_total_value_idr").alias("net_intraday_total_value_idr"),
            sum("realised_gain").alias("intraday_realised_gain"),
            sum("unrealised_gain").alias("intraday_unrealised_gain"),
            sum("realised_gain_in_idr").alias("intraday_realised_gain_idr"),
            sum("unrealised_gain_idr").alias("intraday_unrealised_gain_idr")
        )
        return intraday_snap

    def get_indo_stock_v2_snapshot(self):
        indo_stock_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.indo_stock_returns_v2_snapshots, self.t_1)
        )
        indo_stock_snap = indo_stock_snap.withColumn("total_value", col("total_value").cast(DoubleType()))\
            .withColumn("realised_gain", col("realised_gain").cast(DoubleType()))\
            .withColumn("unrealised_gain", col("unrealised_gain").cast(DoubleType()))\
            .withColumn("dividend", col("dividend").cast(DoubleType())) \
            .withColumn("total_cost", (col("total_quantity")*col("weighted_cost")))

        indo_stock_snap = indo_stock_snap.groupBy("account_id").agg(
            sum("total_value").alias("indo_stock_total_value"),
            sum("realised_gain").alias("indo_stock_realised_gain"),
            sum("unrealised_gain").alias("indo_stock_unrealised_gain"),
            sum("total_cost").alias("indo_stock_total_cost"),
            sum("dividend").alias("indo_stock_dividend")
        )

        return indo_stock_snap

    def get_indo_stock_v2_wallet_snapshot(self):
        indo_stock_wallet_snap = self.io_utils.read_csv_file(
            "{}/{}/dt={}".format(self.bucket_path, S3Paths.indo_stock_wallet_v2, self.t_1)
        )
        indo_stock_wallet_snap = indo_stock_wallet_snap.withColumn("balance", col("balance").cast(DoubleType())) \
            .withColumn("blocked_balance", col("blocked_balance").cast(DoubleType()))
        indo_stock_wallet_snap = indo_stock_wallet_snap.filter((col("balance") > 0) | (col("blocked_balance") > 0))
        indo_stock_wallet_snap = indo_stock_wallet_snap.groupBy("account_id").agg(
            sum("balance").alias("indo_stock_wallet_balance"),
            sum("blocked_balance").alias("indo_stock_wallet_blocked_balance")
        )

        return indo_stock_wallet_snap

    def calculate_agg_values(self, portfolio_snap):
        portfolio_snap = portfolio_snap.withColumn("idrAssetsValue",
                  col("gold_total_value") + col("crypto_total_value")
                + col("crypto_pocket_total_value") + col("fund_total_value")
                + col("cash_balance") + col("free_balance")
                + col("voucher_balance") + col("blocked_cash_balance")
        )

        portfolio_snap = portfolio_snap.withColumn("usdAssetsValueInIdr",
                  col("forex_total_value") + col("stock_total_value_idr")
                + col("fund_usd_total_value_idr") + col("net_global_stock_total_value_idr")
                + col("global_stock_pocket_total_value_idr") + col("leverage_wallet_total_value")
                + col("net_intraday_total_value_idr") + col("options_total_value_idr")
        )

        portfolio_snap = portfolio_snap.withColumn("usdAssetsValueInUsd",
                  col("forex_total_quantity") + col("stock_total_value")
                + col("fund_usd_total_value") + col("net_global_stock_total_value")
                + col("global_stock_pocket_total_value") + col("leverage_wallet_total_quantity")
                + col("net_intraday_total_value") + col("options_total_value")
        )

        portfolio_snap = portfolio_snap.withColumn("realisedGainValue",
                  col("crypto_realised_gain") + col("crypto_pocket_realised_gain")
                + col("forex_realised_gain") + col("fund_realised_gain")
                + col("gold_realised_gain") + col("stock_index_realised_gain_idr")
                + col("global_stock_realised_gain_idr") + col("global_stock_pocket_realised_gain_idr")
                + col("fund_usd_realised_gain_idr") + col("intraday_realised_gain_idr")
                + col("options_realised_gain_idr") + col("crypto_futures_realised_gain")
        )

        portfolio_snap = portfolio_snap.withColumn("unrealisedGainValue",
                   col("crypto_unrealised_gain") + col("crypto_pocket_unrealised_gain")
                 + col("forex_unrealised_gain") + col("fund_unrealised_gain")
                 + col("gold_unrealised_gain") + col("stock_index_unrealised_gain_idr")
                 + col("global_stock_unrealised_gain_idr") + col("global_stock_pocket_unrealised_gain_idr")
                 + col("fund_usd_unrealised_gain_idr") + col("leverage_wallet_unrealised_gain")
                 + col("intraday_unrealised_gain_idr") + col("options_unrealised_gain_idr")
        )

        portfolio_snap = portfolio_snap.withColumn("portfolioValue",
                   col("idrAssetsValue") + col("usdAssetsValueInIdr")
        )
        portfolio_snap = portfolio_snap.withColumn("portfolioAUM",
                  col("idrAssetsValue") + col("usdAssetsValueInIdr")
                - col("crypto_total_reward_value") - col("global_stock_total_reward_value")
                - col("cashback") - col("cash_reward")
        )
        portfolio_snap = portfolio_snap.withColumn("totalCost",
                  col("crypto_total_cost") + col("crypto_pocket_total_cost")
                + col("forex_total_cost") + col("fund_total_cost")
                + col("gold_total_cost") + col("stock_index_total_cost")
                + col("global_stock_total_cost") + col("global_stock_pocket_total_cost")
                + col("fund_usd_total_cost") + col("leverage_wallet_total_cost")
                + col("options_total_cost")
        )

        # select only necessary columns
        cols_to_select = ["account_id", "global_stock_total_dividend", "global_stock_total_dividend_idr",
                          "cashDiffValue", "idrAssetsValue", "usdAssetsValueInIdr",
                          "usdAssetsValueInUsd", "realisedGainValue", "unrealisedGainValue", "portfolioValue",
                          "portfolioAUM", "totalCost"]
        portfolio_snap = portfolio_snap.select(cols_to_select)

        # cast calculated values
        portfolio_snap = portfolio_snap.withColumn("account_id", col("account_id").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("idrAssetsValue", col("idrAssetsValue").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("usdAssetsValueInIdr", col("usdAssetsValueInIdr").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("realisedGainValue", col("realisedGainValue").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("unrealisedGainValue", col("unrealisedGainValue").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("portfolioValue", col("portfolioValue").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("portfolioAUM", col("portfolioAUM").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("cashDiffValue", col("cashDiffValue").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("totalCost", col("totalCost").cast(LongType()))
        portfolio_snap = portfolio_snap.withColumn("usdAssetsValueInUsd", round(col("usdAssetsValueInUsd"), 2))
        created = DateUtils.get_utc_timestamp()
        portfolio_snap = portfolio_snap.withColumn("created", lit(created))

        return portfolio_snap

    def get_all_asset_values(self):
        crypto_currency_snap = self.get_crypto_currency_snapshot()
        crypto_currency_pocket_snap = self.get_crypto_currency_pocket_snapshot()
        global_stock_snap = self.get_global_stock_snapshot()
        global_stock_pocket_snap = self.get_global_stock_pocket_snapshot()
        fund_snap, fund_usd_snap = self.get_fund_snapshot()
        forex_snap = self.get_forex_snapshot()
        gold_snap = self.get_gold_snapshot()
        stock_index_snap = self.get_stock_index_snapshot()
        wallet_snap = self.get_wallet_snapshot()
        gold_gift_and_withdrawal_snap = self.get_gold_gift_and_withdrawal_snapshot()
        leverage_wallet_accounts_snap = self.get_leverage_wallet_accounts_snapshot()
        intraday_snap = self.get_intraday_snapshot()
        options_snap = self.get_global_stock_option_accounts_snapshot()
        crypto_future_position_snap = self.get_crypto_future_positions_snapshot()

        portfolio_snap = gold_snap.join(crypto_currency_snap, on=["account_id"], how="full") \
            .join(crypto_currency_pocket_snap, on=["account_id"], how="full") \
            .join(fund_snap, on=["account_id"], how="full") \
            .join(fund_usd_snap, on=["account_id"], how="full") \
            .join(forex_snap, on=["account_id"], how="full") \
            .join(stock_index_snap, on=["account_id"], how="full") \
            .join(global_stock_snap, on=["account_id"], how="full") \
            .join(global_stock_pocket_snap, on=["account_id"], how="full") \
            .join(wallet_snap, on=["account_id"], how="full") \
            .join(gold_gift_and_withdrawal_snap, on=["account_id"], how="full") \
            .join(leverage_wallet_accounts_snap, on=["account_id"], how="full") \
            .join(intraday_snap, on=["account_id"], how="full") \
            .join(options_snap, on=["account_id"], how="full") \
            .join(crypto_future_position_snap, on=["account_id"], how="full") \
            .fillna(0)

        return portfolio_snap

    def run(self):
        # backfill portfolio snapshot parse start_dt and end_dt and loop through the dates and get the portfolio snapshot
        start_dt = datetime.strptime(self.start_dt, '%Y-%m-%d').date()
        end_dt = datetime.strptime(self.end_dt, '%Y-%m-%d').date()
        current_dt = start_dt
        backfill_accounts = self.io_utils.read_csv_file(
            "{}/backfill/portfolio/accounts/".format(self.bucket_path)
        )
        count_of_backfill_accounts = backfill_accounts.count()
        self.logger.info("Count of backfill accounts: {}".format(count_of_backfill_accounts))
        while current_dt <= end_dt:
            self.t_1 = current_dt
            self.t_2 = (current_dt - timedelta(days=1))
            self.logger.info("Backfilling portfolio snapshot for date: {}".format(self.t_1))
            portfolio_snap = self.get_all_asset_values()
            portfolio_snap = self.calculate_agg_values(portfolio_snap)

            if count_of_backfill_accounts > 0:
                portfolio_snap = portfolio_snap.join(backfill_accounts, on=["account_id"], how="inner")

            self.io_utils.write_csv_file(portfolio_snap,
                "{}/backfill/portfolio/snapshots/dt={}/".format(self.bucket_path, self.t_1)
            )
            current_dt += timedelta(days=1)
        self.spark_utils.stop_spark(self.spark)
