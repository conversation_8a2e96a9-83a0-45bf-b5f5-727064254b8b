from src.utils.spark_utils import *
import time
from datetime import date, timedelta, datetime, timezone


class PortfolioMongoWrite:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        # get configs
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("portfolio_mongo_write")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)
        self.user_props = UserProperties(self.spark, self.config)

        # define local variables
        self.bucket_path = self.config.get("bucket_path")
        self.t_1 = None
        self.t_2 = None
        self.start_dt = kwargs.get("start_dt")
        self.end_dt = kwargs.get("end_dt")
        self.num_of_partition = config["portfolio_mongo_write_num_of_partition"]
        self.sleep_time = config["portfolio_mongo_write_sleep_time"]

        self.write_format = "update"

        self.mongo_collection_shard_key = "{'accountId':1,'createdAt':1}"

    def transform_portfolio_and_write_in_mongo(self):
        portfolio_snap = self.io_utils.read_csv_file(
            "{}/backfill/portfolio/snapshots/dt={}".format(self.bucket_path, self.t_1)
        )
        portfolio_snap = portfolio_snap.filter(
            (col("idrAssetsValue") > 0) | (col("usdAssetsValueInIdr") > 0) |
            (col("realisedGainValue") != 0) | (col("unrealisedGainValue") != 0) |
            (col("portfolioValue") > 0) | (col("portfolioAUM") > 0) |
            (col("cashDiffValue") > 0) | (col("totalCost") > 0) |
            (col("usdAssetsValueInUsd") > 0)
        )

        portfolio_snap = portfolio_snap.withColumn("created", lit(self.t_1))

        self.logger.info("Updating calculated value to mongo")

        for i in range(0, self.num_of_partition):
            self.logger.info("writing in mongo for partition number: {}".format(i))
            df = portfolio_snap.filter(col("account_id") % self.num_of_partition == i)
            self.logger.info("count of df: {}".format(df.count()))
            mongo_config = self.config["data_store"]["reporting_mongo"]
            mongo_config["collection"] = "{}_{}_{}".format(
                self.config["data_store"]["portfolio_snapshot"]["collection"],
                "snapshot", i)

            mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)

            mongo_write_config = {
                "uri": mongo_uri,
                "collection": mongo_config["collection"],
                "batch_size": "500",
                "mode": "append"
            }
            self.io_utils.write_dataset_to_mongo(
                df,
                mongo_config=mongo_write_config,
                asset_name="portfolio snapshot",
                write_format=self.write_format,
                shardkey=self.mongo_collection_shard_key,
                add_created_at=False
            )

    def run(self):
        start_dt = datetime.strptime(self.start_dt, '%Y-%m-%d').date()
        end_dt = datetime.strptime(self.end_dt, '%Y-%m-%d').date()
        current_dt = start_dt
        while current_dt <= end_dt:
            self.t_1 = current_dt
            self.t_2 = (current_dt - timedelta(days=1))
            self.logger.info("Backfilling portfolio snapshot for date: {}".format(self.t_1))
            self.transform_portfolio_and_write_in_mongo()
            current_dt += timedelta(days=1)
        self.spark_utils.stop_spark(self.spark)
