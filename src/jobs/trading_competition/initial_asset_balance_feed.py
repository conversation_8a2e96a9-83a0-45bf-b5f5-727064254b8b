from src.utils.spark_utils import *
from src.utils.date_utils import *

class InitialAssetBalanceFeed:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("Initial Asset Balance Feed")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")

        self.trading_competition_start_time = DateUtils.get_utc_timestamp_from_string(
            self.config["trading_competition"]["start_time"])
        self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2 = DateUtils.get_tc_dates_and_timestamp(self.trading_competition_start_time + timedelta(hours=self.config["trading_competition"]["frequency"]), self.config)
        self.logger.info("t_1: {}, h_1: {}, t_2: {}, h_2: {}, dt_1: {}, dt_2: {}".format(self.t_1, self.h_1, self.t_2, self.h_2, self.dt_1, self.dt_2
        ))

        self.partner_id = self.config["pluang_partner_id"]
        self.usdt_coin_id = self.config["usdt_coin_id"]

        self.gold_returns_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["gold"], self.t_2)
        self.global_stock_returns_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["global_stocks"], self.t_2)
        self.global_stock_pocket_returns_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["global_stocks_pocket"], self.t_2)
        self.crypto_currency_returns_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["crypto_currency"], self.t_2)
        self.crypto_currency_pocket_returns_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["crypto_currency_pocket"], self.t_2)
        self.global_stock_option_accounts_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["global_stock_options"], self.t_2)
        self.crypto_futures_positions_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["crypto_futures"], self.t_2)
        self.global_stock_intraday_accounts_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["global_stock_intraday_accounts"], self.t_2)
        self.global_stock_options_contracts_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["global_stock_options_contracts"], self.t_2)
        self.accounts_path = "{}/{}/dt={}/".format(self.bucket_path, self.config["initial_balance_files"]["accounts"], self.t_2)
        self.start_asset_position_path = "{}/{}/".format(self.bucket_path,
                                                                self.config['start_asset_position_path'])
        self.global_stocks_topic = self.config["kafka_topics"]["global_stock_topic"]

    def get_global_stocks(self):
        self.logger.info("reading global stock codes from kafka topic")
        df_gss_code = self.io_utils.read_from_kafka_in_memory(self.config["bootstrap_servers"], self.global_stocks_topic)
        df_gss_code = df_gss_code.select(col("value.id").alias("global_stock_id"), col("value.stock_type"), col("value.__source_ts_ms").cast(LongType()).alias("__source_ts_ms"))
        df_gss_code = self.ops.de_dupe_dataframe(df_gss_code, ["global_stock_id"], "__source_ts_ms").drop("__source_ts_ms")
        self.logger.info("successfully read global stock codes from kafka")
        return df_gss_code

    def cast_fields(self, txn):
        txn = txn.withColumn("current_unit_price", col("current_unit_price").cast("double"))
        txn = txn.withColumn("executed_quantity", col("executed_quantity").cast("double"))
        txn = txn.withColumn("executed_unit_price", col("executed_unit_price").cast("double"))
        txn = txn.withColumn("executed_total_price", col("executed_total_price").cast("double"))
        txn = txn.withColumn("fees", col("fees").cast("double"))
        return txn

    def get_all_prices(self):
        def get_forex_price():
            forex_partner_price = self.io_utils.read_json_data("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['forex']['price_path'], self.t_2, self.h_2))
            forex_partner_price = forex_partner_price.filter((col("forex_id") == 10000) & (col("partner_id") == self.partner_id))
            forex_price = int(forex_partner_price.collect()[0]["mid_price"])
            return forex_price

        def get_gold_price():
            gold_partner_price = self.io_utils.read_json_data("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['gold']['price_path'], self.t_2, self.h_2))
            gold_partner_price = gold_partner_price.filter(col("partnerId") == self.partner_id).withColumn("mid_price", floor(
                ((col("closeBuyBack").cast(DoubleType()) + col("closeSell").cast(DoubleType())) / 2)))
            gold_price = gold_partner_price.collect()[0]["mid_price"]
            return gold_price

        def get_global_stock_price():
            global_stock_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['global_stock']['price_path'], self.t_2, self.h_2))
            global_stock_price = global_stock_price.withColumn("mid_price", F.round(col("mid_price"), 2))
            global_stock_price = global_stock_price.select(col("_id").alias("global_stock_id"), col("mid_price").alias("current_unit_price"))
            return global_stock_price

        def get_crypto_currency_price():
            crypto_currency_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['crypto_currency']['price_path'], self.t_2, self.h_2))
            crypto_currency_price = crypto_currency_price.withColumn("mid_price", F.round(col("mid_price"), 2))
            crypto_currency_price = crypto_currency_price.select(col("_id").alias("crypto_currency_id"), col("mid_price").alias("current_unit_price"))
            return crypto_currency_price

        def get_usdt_price(crypto_currency_price):
            usdt_price_df = crypto_currency_price.filter(col("crypto_currency_id") == self.usdt_coin_id)
            usdt_price = int(usdt_price_df.collect()[0]["current_unit_price"])
            return usdt_price

        def get_crypto_futures_price():
            crypto_futures_price = self.io_utils.read_csv_file("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['crypto_currency_futures']['price_path'], self.t_2, self.h_2))
            crypto_futures_price = crypto_futures_price.select(col("_id").alias("crypto_future_instrument_id"), col("close_price").alias("current_unit_price"))
            return crypto_futures_price

        def get_fund_price():
            fund_price = self.io_utils.read_json_data("{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['fund']['price_path'], self.t_2, self.h_2))
            fund_price = fund_price.select(col("fund_id"), col("net_asset_value").alias("current_unit_price"))
            return fund_price

        def get_options_price():
            options_price = self.io_utils.read_csv_file("{}/{}/{}/dt={}/hour={}/".format(self.bucket_path, self.config['prices']['global_stock_options']['price_path'], self.config['prices']['global_stock_options']['price_snapshot_folder'], self.t_2, self.h_2))
            options_price = options_price.select(col("optionsContractId").alias("options_contract_id"), col("price").alias("current_unit_price"))
            return options_price

        forex_price = get_forex_price()
        gold_price = get_gold_price()
        global_stock_price = get_global_stock_price()
        crypto_currency_price = get_crypto_currency_price()
        usdt_price = get_usdt_price(crypto_currency_price)
        crypto_futures_price = get_crypto_futures_price()
        fund_price = get_fund_price()
        options_price = get_options_price()
        return usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price

    def get_asset_data(self, usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price):
        def get_gold_data():
            accounts = self.io_utils.read_csv_file(self.accounts_path).select(col("id").alias("account_id"), "user_id", "partner_id")

            gold = self.io_utils.read_csv_file(self.gold_returns_path)
            gold = gold.join(accounts, on=["account_id"], how="left")\
                .filter(col("partner_id") == self.partner_id) \
                .withColumn("current_unit_price", lit(gold_price))
            gold = gold.withColumn("executed_unit_price", col("current_unit_price")).select(lit("gold").alias("asset_type"), lit("gold_transactions").alias("asset_sub_type"),
                               lit(1).alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), lit(self.trading_competition_start_time).alias("created"), lit(self.trading_competition_start_time).alias("updated"), col("total_quantity").alias("executed_quantity"), "executed_unit_price",
                               (col("total_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"), lit("BUY").alias("transaction_type"), lit("SUCCESS").alias("status"), lit(1).alias("currency_to_idr"), lit(self.trading_competition_start_time).alias("transaction_time"),
                               lit(0).alias("leverage"), lit(1).alias("current_currency_to_idr"), "current_unit_price")
            gold = self.cast_fields(gold)
            return gold

        def get_crypto_data():
            crypto_returns = self.io_utils.read_csv_file(self.crypto_currency_returns_path).select("account_id", "user_id", "crypto_currency_id", "total_quantity")
            crypto_pocket_returns = self.io_utils.read_csv_file(self.crypto_currency_pocket_returns_path).select("account_id", "user_id", "crypto_currency_id", "total_quantity")
            crypto = crypto_returns.union(crypto_pocket_returns)
            crypto = crypto.groupBy(["account_id", "user_id", "crypto_currency_id"]).agg(sum("total_quantity").alias("total_quantity"))
            crypto = crypto.withColumn("created", lit(self.trading_competition_start_time)) \
                .withColumn("updated", lit(self.trading_competition_start_time)) \
                .withColumn("transaction_time", lit(self.trading_competition_start_time))
            crypto = crypto.join(crypto_currency_price, on=["crypto_currency_id"], how="left").fillna(0.0)
            crypto = crypto.withColumn("executed_unit_price", col("current_unit_price")).select(lit("crypto_currency").alias("asset_type"), lit("crypto_currency_transactions").alias("asset_sub_type"),
                                                                                  col("crypto_currency_id").alias("asset_id"), "account_id", "user_id", col("account_id").alias("transaction_id"), "created", "updated", col("total_quantity").alias("executed_quantity"), "executed_unit_price",
                                                                                  (col("total_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"), lit("BUY").alias("transaction_type"), lit("SUCCESS").alias("status"), lit(1).alias("currency_to_idr"), "transaction_time",
                                                                                  lit(0).alias("leverage"), lit(1).alias("current_currency_to_idr"), "current_unit_price")
            crypto = self.cast_fields(crypto)
            return crypto

        def get_global_stocks_data():
            global_stock_types = self.get_global_stocks()
            global_stock_returns = self.io_utils.read_csv_file(self.global_stock_returns_path).select("account_id", "user_id", "global_stock_id", "total_quantity")
            global_stock_pocket_returns = self.io_utils.read_csv_file(self.global_stock_pocket_returns_path).select("account_id", "user_id", "global_stock_id", "total_quantity")
            global_stock = global_stock_returns.union(global_stock_pocket_returns)

            global_stock = global_stock.groupBy(["account_id", "user_id", "global_stock_id"]).agg(sum("total_quantity").alias("total_quantity"))
            global_stock = global_stock.withColumn("created", lit(self.trading_competition_start_time)) \
                .withColumn("updated", lit(self.trading_competition_start_time)) \
                .withColumn("transaction_time", lit(self.trading_competition_start_time))
            global_stock = global_stock.join(global_stock_types, on=["global_stock_id"], how="left")
            global_stock = global_stock.withColumn("leverage", when(col("stock_type") == "CFD_LEVERAGE", 2).otherwise(0))
            global_stock = global_stock.join(global_stock_price, on=["global_stock_id"], how="left").fillna(0.0)
            global_stock = global_stock.withColumn("executed_unit_price", col("current_unit_price")).select(lit("global_stocks").alias("asset_type"), lit("global_stock_transactions").alias("asset_sub_type"),
                                                                                  col("global_stock_id").alias("asset_id"), "account_id", "user_id", col("account_id").alias("transaction_id"), "created", "updated", col("total_quantity").alias("executed_quantity"), "executed_unit_price",
                                                                                  (col("total_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"), lit("BUY").alias("transaction_type"), lit("SUCCESS").alias("status"), lit(forex_price).alias("currency_to_idr"), "transaction_time",
                                                                                  "leverage", lit(forex_price).alias("current_currency_to_idr"), "current_unit_price")
            global_stock = self.cast_fields(global_stock)
            return global_stock

        def get_global_stock_intraday_data():
            global_stock_intraday = self.io_utils.read_csv_file(self.global_stock_intraday_accounts_path)
            global_stock_intraday = global_stock_intraday.withColumn("leverage", lit(4))
            global_stock_intraday = global_stock_intraday.join(global_stock_price, on=["global_stock_id"], how="left").fillna(0.0)
            global_stock_intraday = global_stock_intraday.withColumn("executed_unit_price", col("current_unit_price")).select(lit("global_stocks").alias("asset_type"), lit("global_stock_transactions").alias("asset_sub_type"),
                                                                                    col("global_stock_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), lit(self.trading_competition_start_time).alias("created"), lit(self.trading_competition_start_time).alias("updated"), col("total_quantity").alias("executed_quantity"), "executed_unit_price",
                                                                                    (col("total_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"), lit("BUY").alias("transaction_type"), lit("SUCCESS").alias("status"), lit(forex_price).alias("currency_to_idr"), lit(self.trading_competition_start_time).alias("transaction_time"),
                                                                                    "leverage", lit(forex_price).alias("current_currency_to_idr"), "current_unit_price")
            global_stock_intraday = self.cast_fields(global_stock_intraday)
            return global_stock_intraday

        def get_options_data():
            options = self.io_utils.read_csv_file(self.global_stock_option_accounts_path)
            options_contracts = self.io_utils.read_csv_file(self.global_stock_options_contracts_path).select(col("id").alias("options_contract_id"), "shares_per_contract")
            options = options.join(options_contracts, on=["options_contract_id"], how="left")
            options = options.withColumn("total_quantity", col("total_quantity") * col("shares_per_contract")).drop("shares_per_contract")
            options = options.withColumn("leverage", lit(0))
            options = options.join(options_price, on=["options_contract_id"], how="left").fillna(0.0)
            options = options.withColumn("executed_unit_price", col("current_unit_price")).select(lit("global_stock_options").alias("asset_type"), lit("options_contract_transactions").alias("asset_sub_type"),
                                                                                     col("options_contract_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), lit(self.trading_competition_start_time).alias("created"), lit(self.trading_competition_start_time).alias("updated"), col("total_quantity").alias("executed_quantity"), "executed_unit_price",
                                                                                     (col("total_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"), lit("LONG_OPEN").alias("transaction_type"), lit("SUCCESS").alias("status"), lit(forex_price).alias("currency_to_idr"), lit(self.trading_competition_start_time).alias("transaction_time"),
                                                                                     "leverage", lit(forex_price).alias("current_currency_to_idr"), "current_unit_price")
            options = self.cast_fields(options)
            return options

        def get_crypto_futures_data():
            futures = self.io_utils.read_parquet_data(self.crypto_futures_positions_path)
            futures = futures.withColumn("leverage", lit(25))
            futures = futures.join(crypto_futures_price, on=["crypto_future_instrument_id"], how="left").fillna(0.0)
            futures = futures.withColumn("executed_unit_price", col("current_unit_price")).select(lit("crypto_futures").alias("asset_type"), lit("crypto_future_trades").alias("asset_sub_type"),
                                                                                                  col("crypto_future_instrument_id").alias("asset_id"), "account_id", "user_id", col("id").alias("transaction_id"), lit(self.trading_competition_start_time).alias("created"), lit(self.trading_competition_start_time).alias("updated"), col("total_quantity").alias("executed_quantity"), "executed_unit_price",
                                                                                                  (col("total_quantity") * col("executed_unit_price")).cast("double").alias("executed_total_price"), lit(0).alias("fees"), lit("BUY").alias("transaction_type"), lit("SUCCESS").alias("status"), lit(usdt_price).alias("currency_to_idr"), lit(self.trading_competition_start_time).alias("transaction_time"),
                                                                                                  "leverage", lit(usdt_price).alias("current_currency_to_idr"), "current_unit_price")
            futures = self.cast_fields(futures)
            return futures

        gold = get_gold_data()
        options = get_options_data()
        crypto = get_crypto_data()
        futures = get_crypto_futures_data()
        global_stock = get_global_stocks_data()
        global_stock_intraday = get_global_stock_intraday_data()
        return gold, options, crypto, futures, global_stock, global_stock_intraday

    def execute(self):

        usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price = self.get_all_prices()

        gold, options, crypto, futures, global_stock, global_stock_intraday = self.get_asset_data(usdt_price, forex_price, gold_price, global_stock_price, crypto_currency_price, fund_price, options_price, crypto_futures_price)

        all_txn = gold.union(crypto).union(options).union(futures).union(global_stock).union(global_stock_intraday)
        all_txn = all_txn.filter(col("executed_quantity") != 0)

        all_txn = all_txn.withColumn("forex_price", lit(forex_price)) \
            .withColumn("usdt_price", lit(usdt_price))
        self.io_utils.write_parquet_file(all_txn, self.start_asset_position_path, 5)

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)
