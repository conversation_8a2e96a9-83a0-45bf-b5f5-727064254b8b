from src.utils.spark_utils import *
from src.utils.date_utils import *

class InitialPriceFeed:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # get utility objects
        self.spark_utils = SparkUtils("Initial Price Feed")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        self.bucket_path = self.config.get("bucket_path")
        self.dest_path = "{}/{}".format(self.bucket_path, "trading_competition_2025_003/prices")
        self.dt = kwargs.get("initial_price_cutoff_date")

    def copy_all_prices(self):
        # copy gold price
        src_gold_path = "{}/gold_prices/partner_gold_price_stats_ohlc/dt={}/".format(self.bucket_path, self.dt)
        dst_gold_path = "{}/gold_prices/dt={}/hour=24/".format(self.dest_path, self.dt)
        gold_df = self.io_utils.read_json_data(src_gold_path)
        self.io_utils.write_json_file(gold_df, dst_gold_path)

        # copy global stock price
        src_global_stock_path = "{}/global_stocks_prices/mongo_hourly_ohlc_price/dt={}/".format(self.bucket_path, self.dt)
        dst_global_stock_path = "{}/global_stocks_prices/dt={}/hour=24/".format(self.dest_path, self.dt)
        global_stock_price = self.io_utils.read_csv_file(src_global_stock_path)
        self.io_utils.write_csv_file(global_stock_price, dst_global_stock_path)

        # copy crypto currency price
        src_crypto_currency_path = "{}/crypto_currency_prices/mongo_hourly_ohlc_price/dt={}/".format(self.bucket_path, self.dt)
        dst_crypto_currency_path = "{}/crypto_currency_prices/dt={}/hour=24/".format(self.dest_path, self.dt)
        crypto_currency_price = self.io_utils.read_csv_file(src_crypto_currency_path)
        self.io_utils.write_csv_file(crypto_currency_price, dst_crypto_currency_path)

        # copy fund price
        src_fund_path = "{}/fund_prices/fund_partner_price/dt={}/".format(self.bucket_path, self.dt)
        dst_fund_path = "{}/fund_prices/dt={}/hour=24/".format(self.dest_path, self.dt)
        fund_price = self.io_utils.read_json_data(src_fund_path)
        self.io_utils.write_json_file(fund_price, dst_fund_path)

        # copy forex price
        src_forex_path = "{}/forex_prices/forex_partner_price/dt={}/".format(self.bucket_path, self.dt)
        dst_forex_path = "{}/forex_prices/dt={}/hour=24/".format(self.dest_path, self.dt)
        forex_price = self.io_utils.read_json_data(src_forex_path)
        self.io_utils.write_json_file(forex_price, dst_forex_path)

        # copy global stock options price
        src_global_stock_options_path = "{}/global_stock_options_price/snapshots/dt={}/".format(self.bucket_path, self.dt)
        dst_global_stock_options_path = "{}/global_stock_options_price/snapshots/dt={}/hour=24/".format(self.dest_path,self.dt)
        global_stock_options_price = self.io_utils.read_csv_file(src_global_stock_options_path)
        self.io_utils.write_csv_file(global_stock_options_price, dst_global_stock_options_path)

        # copy crypto future price
        src_crypto_futures_path = "{}/crypto_currency_futures_prices/crypto_futures_hourly_ohlc_price_stats/dt={}/".format(self.bucket_path, self.dt)
        dst_crypto_futures_path = "{}/crypto_currency_futures_prices/dt={}/hour=24/".format(self.dest_path, self.dt)
        crypto_futures_price = self.io_utils.read_csv_file(src_crypto_futures_path)
        self.io_utils.write_csv_file(crypto_futures_price, dst_crypto_futures_path)

        # copy options contracts
        src_options_contracts_path = "{}/global_stock_options_contracts/t_2_files/dt={}/".format(self.bucket_path, self.dt)
        dst_options_contracts_path = "{}/trading_competition_2025_003/snapshots/options_contracts/dt={}/hour=24/".format(self.bucket_path, self.dt)
        options_contracts_price = self.io_utils.read_csv_file(src_options_contracts_path)
        self.io_utils.write_parquet_file(options_contracts_price, dst_options_contracts_path)

    def execute(self):
        self.copy_all_prices()

    def run(self):
        self.execute()
        self.spark_utils.stop_spark(self.spark)

