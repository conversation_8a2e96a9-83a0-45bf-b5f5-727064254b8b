"""
EMR Job Wrapper for Crypto Transaction Processing
Provides a job wrapper class that can be instantiated by master_transaction_main.py dispatcher.
Uses CryptoProcessor from transformers for processing logic.
"""

from typing import Dict, Any
from pyspark.sql.functions import lit
from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils
from src.jobs.master_transactions.transformers.crypto_processor import CryptoProcessor


class CryptoTransactionJob:
    """
    EMR-compatible job wrapper for crypto transaction processing.
    This class follows the expected pattern for the job dispatcher.
    Instantiated by master_transaction_main.py with config and kwargs.
    """
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        """Initialize the crypto transaction job with config and kwargs."""
        # Load master transaction specific configuration using ConfigLoader
        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()

        # Merge with the generic config, giving priority to master transaction config
        self.config = {**config, **master_config}
        self.kwargs = kwargs
        
        # Extract common parameters from config (set by main.py) or kwargs (for direct calls)
        self.t_1 = self.config.get('t_1') or kwargs.get('t_1')
        self.h_1 = self.config.get('h_1') or kwargs.get('h_1') or self.t_1
        
        # Create Spark session using SparkUtils pattern
        self.spark_utils = SparkUtils("CryptoTransactionJob")
        self.spark = self.spark_utils.create_spark_session()
        print("✅ Spark session created for CryptoTransactionJob")
        print(f"✅ Configuration loaded with keys: {list(master_config.keys())}")
    
    def execute(self):
        """Main execution method called by EMR framework."""
        print("🚀 Starting Crypto Transaction Processing")
        print(f"Processing date: {self.t_1}")
        print(f"Processing hour: {self.h_1}")
        
        try:
            # Use CryptoProcessor from transformers
            processor = CryptoProcessor(
                config=self.config,
                t_1=self.t_1,
                h_1=self.h_1,
                spark=self.spark
            )
            
            # Process all crypto sources
            result_df = processor.process_all_crypto_sources()
            
            if result_df is not None:
                # Write results to S3
                bucket_name = self.config.get("bucket", "pluang-datalake-calculated-staging")
                output_base = self.config.get("master_transaction_path", f"s3a://{bucket_name}/processed_data/master_transactions/")
                output_path = f"{output_base}dt={self.t_1}/"
                if self.h_1 and self.h_1 != self.t_1:
                    output_path += f"hour={self.h_1}/"
                
                print(f"Writing crypto results to: {output_path}")
                
                # Debug: Print schema to identify void columns
                print("🔍 DataFrame schema before writing:")
                result_df.printSchema()
                
                # Check for void columns and fix them
                void_columns = []
                for field in result_df.schema.fields:
                    field_type_str = str(field.dataType)
                    if field_type_str in ["void", "NullType()", "NullType"] or "NullType" in field_type_str:
                        void_columns.append(field.name)
                        print(f"⚠️  Found void column: {field.name} (type: {field.dataType})")
                
                if void_columns:
                    print(f"🔧 Fixing {len(void_columns)} void columns...")
                    for col_name in void_columns:
                        if col_name in ["contract_id", "contract_name", "product", "product_id", 
                                       "recurring_transaction_id", "user_pocket_id", "idr2usd_ref_id", 
                                       "usd2usdmargin_ref_id", "usdmargin2usd_ref_id", "ref_id_hedge", "network"]:
                            # String columns - use empty string
                            result_df = result_df.withColumn(col_name, lit("").cast("string"))
                        else:
                            # Numeric columns - use null with appropriate type
                            result_df = result_df.withColumn(col_name, lit(None).cast("decimal(20,8)"))
                    print("✅ Fixed all void columns")
                
                result_df.coalesce(2).write.mode("overwrite").parquet(output_path)
                
                print(f"✅ Successfully processed {result_df.count()} crypto transactions")
            else:
                print("⚠️  No crypto data to write")
                
        except Exception as e:
            print(f"❌ Crypto processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise e
    
    def run(self):
        """Run method for compatibility with job dispatcher."""
        try:
            self.execute()
        finally:
            if hasattr(self, 'spark_utils'):
                self.spark_utils.stop_spark(self.spark)
        
        print("🏁 Crypto transaction processing completed")

