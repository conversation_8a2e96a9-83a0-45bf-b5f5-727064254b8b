"""
Crypto Transaction Processor
Coordinates processing of all crypto transaction sources using specialized processors.
Uses factory pattern internally for multiple crypto sources.
"""

from typing import List, Dict, Any, Optional
from pyspark.sql import DataFrame
from pyspark.sql.functions import lit, col
from src.utils.custom_logger import get_logger
from src.utils.asset_utils import AssetUtils
from .crypto_processors import CryptoProcessorFactory, BaseCryptoProcessor


class CryptoProcessor:
    """
    Orchestrates processing of all crypto transaction sources.
    Uses the factory pattern to create specialized processors for each source.
    """
    
    def __init__(self, config: Dict[str, Any], t_1: str, h_1: str = None, spark=None):
        self.config = config
        self.t_1 = t_1
        self.h_1 = h_1 if h_1 else t_1
        self.logger = get_logger()
        
        # Get or create Spark session
        if spark is None:
            from pyspark.sql import SparkSession
            self.spark = SparkSession.getActiveSession()
            if self.spark is None:
                raise RuntimeError("No active Spark session found. Please create one first.")
        else:
            self.spark = spark
        
        # Initialize IO utils for price loading (import locally to avoid circular import)
        from src.utils.io_utils import IOUtils
        self.io_utils = IOUtils(self.spark, self.config)
        
        # Initialize asset utils for real-time Kafka loading
        self.asset_utils = AssetUtils(self.spark, self.config)
        
        # Load price data and crypto currencies lookup
        self.price_df = self._load_crypto_quote_and_cover_prices()
        self.advanced_price_df = self._load_crypto_advanced_prices()
        self.crypto_currencies_df = self._load_crypto_currencies_from_kafka()
        
        # Debug logging
        if self.crypto_currencies_df is not None:
            self.logger.info(f"🔍 DEBUG: crypto_currencies_df loaded successfully with {self.crypto_currencies_df.count()} records")
        else:
            self.logger.warning("🔍 DEBUG: crypto_currencies_df is None!")
        
        # Initialize all crypto processors with required parameters
        self.processors = CryptoProcessorFactory.create_all_processors(
            self.spark, config, t_1, self.h_1
        )
    
    def _load_crypto_quote_and_cover_prices(self) -> DataFrame:
        """Load crypto currency price data from CSV for quote and cover orders."""
        try:
            # Build price path based on configuration
            if "prices" in self.config and "crypto_currency" in self.config["prices"]:
                price_config = self.config["prices"]["crypto_currency"]
                
                # Check for new dual-path configuration first, fallback to old single path
                if "quote_and_cover_orders_price_path" in price_config:
                    price_path = price_config["quote_and_cover_orders_price_path"]
                else:
                    self.logger.warning("No quote and cover orders price path configured")
                    return None
                
                # Handle both S3 and local file paths
                if price_path.startswith("file://"):
                    full_price_path = f"{price_path}/dt={self.t_1}/"
                else:
                    bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
                    full_price_path = f"{bucket_path}{price_path}/dt={self.t_1}/"
                
                self.logger.info(f"Loading crypto quote and cover prices from: {full_price_path}")
                
                # Try to load price data
                try:
                    price_df = self.io_utils.read_csv_file(full_price_path)
                    
                    if price_df is not None:
                        count = price_df.count()
                        self.logger.info(f"✅ Loaded {count} crypto quote and cover price records")
                        self.logger.info(f"Quote and cover price columns: {price_df.columns}")
                        
                        # Rename _id to asset_id for joining
                        if "_id" in price_df.columns:
                            price_df = price_df.withColumnRenamed("_id", "asset_id")
                        
                        # Add current_unit_price column using mid_price
                        if "mid_price" in price_df.columns:
                            price_df = price_df.withColumnRenamed("mid_price", "current_unit_price")
                        
                        return price_df
                    else:
                        self.logger.warning("Quote and cover price DataFrame is None")
                        return None
                        
                except Exception as e:
                    self.logger.warning(f"Could not load quote and cover price data: {str(e)}")
                    return None
            else:
                self.logger.warning("No crypto price configuration found")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading crypto quote and cover prices: {str(e)}")
            return None
    
    def _load_crypto_advanced_prices(self) -> DataFrame:
        """Load crypto currency price data from JSON for advanced orders."""
        try:
            # Build price path based on configuration
            if "prices" in self.config and "crypto_currency" in self.config["prices"]:
                price_config = self.config["prices"]["crypto_currency"]
                
                if "advanced_orders_price_path" not in price_config:
                    self.logger.info("No advanced orders price path configured - skipping advanced price loading")
                    return None
                
                price_path = price_config["advanced_orders_price_path"]
                
                # Handle both S3 and local file paths
                if price_path.startswith("file://"):
                    full_price_path = f"{price_path}/dt={self.t_1}/"
                else:
                    bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
                    full_price_path = f"{bucket_path}{price_path}/dt={self.t_1}/"
                
                self.logger.info(f"Loading crypto advanced prices from: {full_price_path}")
                
                # Try to load price data
                try:
                    price_df = self.io_utils.read_json_data(full_price_path, True, None, False)
                    
                    if price_df is not None:
                        count = price_df.count()
                        self.logger.info(f"✅ Loaded {count} crypto advanced price records")
                        self.logger.info(f"Advanced price columns: {price_df.columns}")
                        
                        # Extract from nested structure if needed
                        if "value" in price_df.columns:
                            price_df = price_df.select(col("value.*"))
                        
                        # Transform to match expected schema
                        # Advanced orders have different structure - need to extract price from tradeDetails or averagePrice
                        
                        return price_df
                    else:
                        self.logger.warning("Advanced price DataFrame is None")
                        return None
                        
                except Exception as e:
                    self.logger.warning(f"Could not load advanced price data: {str(e)}")
                    return None
            else:
                self.logger.warning("No crypto price configuration found")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading crypto advanced prices: {str(e)}")
            return None
    
        """Load crypto currency price data - combines quote/cover and advanced order prices."""
        quote_and_cover_prices = self._load_crypto_quote_and_cover_prices()
        advanced_prices = self._load_crypto_advanced_prices()
        
        # For now, return quote and cover prices as the primary source
        # Advanced prices will be used by specific processors that need them
        return quote_and_cover_prices
        """Load advanced order info data from JSON."""
        try:
            # Build advanced order info path based on configuration
            if "advanced_order_info" in self.config and "crypto_currency" in self.config["advanced_order_info"]:
                advanced_order_info_config = self.config["advanced_order_info"]["crypto_currency"]
                advanced_order_info_path = advanced_order_info_config["advanced_order_info_path"]
                
                # Handle both S3 and local file paths
                if advanced_order_info_path.startswith("file://"):
                    full_advanced_order_info_path = f"{advanced_order_info_path}/dt={self.t_1}/"
                else:
                    bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
                    full_advanced_order_info_path = f"{bucket_path}{advanced_order_info_path}/dt={self.t_1}/"
                
                self.logger.info(f"Loading advanced order info from: {full_advanced_order_info_path}")
                
                # Try to load advanced order info data
                try:
                    advanced_order_info_df = self.io_utils.read_json_data(full_advanced_order_info_path)
                    
                    if advanced_order_info_df is not None:
                        count = advanced_order_info_df.count()
                        self.logger.info(f"✅ Loaded {count} advanced order info records")
                        self.logger.info(f"Advanced order info columns: {advanced_order_info_df.columns}")
                        
                        return advanced_order_info_df
                    else:
                        self.logger.warning("Advanced order info DataFrame is None")
                        return None
                        
                except Exception as e:
                    self.logger.warning(f"Could not load advanced order info data: {str(e)}")
                    return None
            else:
                self.logger.warning("No advanced order info configuration found")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading advanced order info: {str(e)}")
            return None

    def _load_crypto_currencies_from_kafka(self) -> DataFrame:
        """Load crypto currencies lookup data from Kafka using AssetUtils."""
        try:
            self.logger.info("Loading crypto currencies using AssetUtils from Kafka")
            
            # Use AssetUtils to get crypto assets from Kafka
            crypto_currencies_df = self.asset_utils.get_crypto_assets()
            
            if crypto_currencies_df is not None and crypto_currencies_df.count() > 0:
                # Rename columns to match master transactions schema
                crypto_currencies_df = crypto_currencies_df.select(
                    col("id").alias("id"),
                    col("symbol").alias("symbol"),
                    col("symbol").alias("product"),  # Use symbol as product for master transactions
                    col("active").alias("active"),
                    col("updated").alias("updated")
                )
                
                self.logger.info(f"✅ Loaded {crypto_currencies_df.count()} crypto currencies from Kafka")
                return crypto_currencies_df
            else:
                self.logger.error("Failed to load crypto currencies from Kafka")
                return None
                
        except Exception as e:
            self.logger.error(f"Error loading crypto currencies from Kafka: {str(e)}")
            return None
    
    def _load_crypto_currencies_legacy(self) -> DataFrame:
        """Legacy method: Load crypto currencies lookup data from S3 JSON only."""
        try:
            # Build crypto currencies path
            crypto_currencies_path = self.config.get("crypto_currencies_path")
            if not crypto_currencies_path:
                # Default path if not configured
                if "transactions_snapshot_path" in self.config:
                    base_path = self.config["transactions_snapshot_path"]
                    crypto_currencies_path = f"{base_path}crypto_currencies/dt={self.t_1}/"
                else:
                    self.logger.warning("No crypto_currencies_path configured and no transactions_snapshot_path found")
                    return None
            else:
                # Handle both S3 and local file paths
                if crypto_currencies_path.startswith("file://"):
                    crypto_currencies_path = f"{crypto_currencies_path}/dt={self.t_1}/"
                else:
                    bucket_path = self.config.get("bucket_path", "s3a://pluang-datalake-calculated-staging/")
                    crypto_currencies_path = f"{bucket_path}{crypto_currencies_path}/dt={self.t_1}/"
            
            self.logger.info(f"Loading crypto currencies from: {crypto_currencies_path}")
            
            try:
                # Read JSON data (new flat structure - no nested 'value' field)
                currencies_df = self.io_utils.read_json_data(crypto_currencies_path)
                
                if currencies_df is not None:
                    count = currencies_df.count()
                    self.logger.info(f"✅ Loaded {count} crypto currency records")
                    
                    # Select only needed columns: id and symbol (flat structure)
                    currencies_df = currencies_df.select(
                        col("id").alias("crypto_currency_id"),
                        col("symbol").alias("product")
                    )
                    
                    return currencies_df
                else:
                    self.logger.warning("Crypto currencies DataFrame is None")
                    return None
                    
            except Exception as e:
                self.logger.error(f"Failed to load crypto currencies data: {e}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error in _load_crypto_currencies: {e}")
            return None
    
    def process_all_crypto_sources(self) -> DataFrame:
        """
        Process all crypto transaction sources and combine results.
        Each processor handles one specific crypto source from the SQL.
        """
        self.logger.info("=== Starting Crypto Transaction Processing ===")
        self.logger.info(f"Processing {len(self.processors)} crypto sources")
        
        results = []
        
        for processor in self.processors:
            try:
                source_name = processor._get_source_name()
                self.logger.info(f"=== Processing {source_name} ===")
                
                # Debug logging before processing
                self.logger.info(f"🔍 DEBUG: About to process {source_name} with crypto_currencies_df: {self.crypto_currencies_df is not None}")
                
                # Process this crypto source with price data
                result_df = processor.process_transactions(
                    price_df=self.price_df, 
                    advanced_price_df=self.advanced_price_df,
                    crypto_currencies_df=self.crypto_currencies_df
                )
                
                if result_df is not None and result_df.count() > 0:
                    self.logger.info(f"✅ {source_name}: {result_df.count()} transactions processed")
                    results.append(result_df)
                else:
                    self.logger.info(f"⚠️  {source_name}: No data found or processed")
                    
            except Exception as e:
                source_name = processor._get_source_name() if hasattr(processor, '_get_source_name') else processor.__class__.__name__
                self.logger.error(f"❌ Failed to process {source_name}: {str(e)}")
                import traceback
                self.logger.error(f"Full traceback: {traceback.format_exc()}")
                # Continue processing other sources
                continue
        
        # Combine all results
        if not results:
            self.logger.warning("No crypto transaction data processed from any source")
            return None
        
        self.logger.info(f"=== Combining {len(results)} crypto sources ===")
        
        # Union all DataFrames
        combined_df = results[0]
        for i, df in enumerate(results[1:], 1):
            self.logger.info(f"Combining source {i+1}/{len(results)}")
            combined_df = combined_df.unionByName(df, allowMissingColumns=True)
        
        total_count = combined_df.count()
        self.logger.info(f"✅ Total crypto transactions processed: {total_count}")
        
        return combined_df
    
    def process_specific_source(self, source_name: str) -> DataFrame:
        """
        Process a specific crypto source by name.
        Useful for testing and validation of individual sources.
        """
        self.logger.info(f"=== Processing specific crypto source: {source_name} ===")
        
        try:
            processor = CryptoProcessorFactory.create_processor_by_name(
                source_name, self.spark, self.config, self.t_1, self.h_1
            )
            
            result_df = processor.process_transactions(price_df=self.price_df)
            
            if result_df is not None:
                count = result_df.count()
                self.logger.info(f"✅ {source_name}: {count} transactions processed")
                return result_df
            else:
                self.logger.info(f"⚠️  {source_name}: No data found")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Failed to process {source_name}: {str(e)}")
            import traceback
            self.logger.error(f"Full traceback: {traceback.format_exc()}")
            raise
    
    def get_source_summary(self) -> Dict[str, Any]:
        """
        Get summary information about all crypto sources.
        """
        summary = {
            "total_processors": len(self.processors),
            "available_sources": CryptoProcessorFactory.get_available_processors(),
            "processor_info": CryptoProcessorFactory.get_processor_info(),
            "config": {
                "t_1": self.t_1,
                "h_1": self.h_1,
                "bucket_path": self.config.get("bucket_path", ""),
                "transactions_snapshot_path": self.config.get("transactions_snapshot_path", "")
            }
        }
        
        return summary
    
    def validate_against_sql_counts(self, expected_counts: Dict[str, int]) -> Dict[str, Dict[str, Any]]:
        """
        Validate processed counts against expected SQL counts.
        
        Args:
            expected_counts: Dict mapping source names to expected row counts
            
        Returns:
            Dict with validation results for each source
        """
        self.logger.info("=== Validating crypto sources against SQL counts ===")
        
        validation_results = {}
        
        for source_name in CryptoProcessorFactory.get_available_processors():
            try:
                # Process this specific source
                result_df = self.process_specific_source(source_name)
                actual_count = result_df.count() if result_df is not None else 0
                expected_count = expected_counts.get(source_name, 0)
                
                # Calculate validation metrics
                difference = actual_count - expected_count
                percentage_diff = (difference / expected_count * 100) if expected_count > 0 else 0
                
                validation_results[source_name] = {
                    "actual_count": actual_count,
                    "expected_count": expected_count,
                    "difference": difference,
                    "percentage_diff": round(percentage_diff, 2),
                    "status": "✅ PASS" if abs(percentage_diff) <= 5 else "❌ FAIL"  # 5% tolerance
                }
                
                self.logger.info(f"{source_name}: {actual_count} vs {expected_count} ({percentage_diff:+.1f}%)")
                
            except Exception as e:
                validation_results[source_name] = {
                    "actual_count": 0,
                    "expected_count": expected_counts.get(source_name, 0),
                    "difference": -expected_counts.get(source_name, 0),
                    "percentage_diff": -100,
                    "status": "❌ ERROR",
                    "error": str(e)
                }
                self.logger.error(f"{source_name}: Processing failed - {str(e)}")
        
        return validation_results
    
    def refresh_crypto_currencies(self) -> bool:
        """
        Refresh crypto currencies data from Kafka during runtime.
        Useful for long-running jobs that need updated currency information.
        
        Returns:
            True if refresh was successful, False otherwise
        """
        try:
            self.logger.info("🔄 Refreshing crypto currencies data from Kafka using AssetUtils")
            
            # Refresh using AssetUtils
            refreshed_df = self.asset_utils.get_crypto_assets()
            
            if refreshed_df is not None and refreshed_df.count() > 0:
                # Rename columns to match master transactions schema
                refreshed_df = refreshed_df.select(
                    col("id").alias("id"),
                    col("symbol").alias("symbol"),
                    col("symbol").alias("product"),  # Use symbol as product for master transactions
                    col("active").alias("active"),
                    col("updated").alias("updated")
                )
                
                # Update the cached DataFrame
                self.crypto_currencies_df = refreshed_df
                
                # Update all processors with new crypto currencies data
                for processor in self.processors:
                    if hasattr(processor, 'update_crypto_currencies'):
                        processor.update_crypto_currencies(refreshed_df)
                
                record_count = refreshed_df.count()
                self.logger.info(f"✅ Successfully refreshed {record_count} crypto currencies from Kafka")
                return True
            else:
                self.logger.error("Failed to refresh crypto currencies data")
                return False
                
        except Exception as e:
            self.logger.error(f"Error refreshing crypto currencies: {str(e)}")
            return False
    
    def get_crypto_currencies_for_join(self) -> Optional[DataFrame]:
        """
        Get crypto currencies data optimized for joining with transaction data.
        
        Returns:
            DataFrame with minimal columns needed for joins
        """
        if self.crypto_currencies_df is not None:
            # Use already loaded data for efficiency
            join_df = self.crypto_currencies_df.select(
                col("id").alias("asset_id"),     # For joining with transaction data
                col("product").alias("product"), # Product name for master transactions
                col("symbol").alias("symbol"),   # Symbol for reference
                col("active").alias("active")    # Active status for filtering
            ).filter(col("active") == True)     # Only active currencies
            
            self.logger.info(f"Prepared {join_df.count()} active crypto currencies for joining")
            return join_df
        else:
            # Load fresh data if not available
            return self.crypto_currencies_loader.get_crypto_currencies_for_join(
                t_1=self.t_1, 
                use_kafka=True
            )
