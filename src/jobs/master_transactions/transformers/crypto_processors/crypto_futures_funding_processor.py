"""
Crypto Futures Funding Processor
Handles pluang_crypto_futures__crypto_future_funding_transactions → unified schema.
Conservative mapping with column-existence guards to avoid runtime errors.
"""

from typing import Dict, Any, Optional
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when
from .base_crypto_processor import BaseCryptoProcessor


class CryptoFuturesFundingProcessor(BaseCryptoProcessor):
    def _get_source_name(self) -> str:
        return "crypto_future_funding_transactions"

    def _get_base_transaction_data(self) -> Optional[DataFrame]:
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")

        # Path pattern mirrors other crypto sources (without dataset prefix)
        if self.h_1 == self.t_1:
            s3_path = f"{self.transactions_snapshot_path}{source_name}/dt={self.t_1}/"
        else:
            s3_path = f"{self.transactions_snapshot_path}{source_name}/dt={self.t_1}/hour={self.h_1}/"

        df = self._read_data_with_error_handling(s3_path, source_name, source_name)
        if df is None:
            return None
        if df.count() == 0:
            self.logger.warning(f"Empty DataFrame for {source_name}")
            return None

        # Some snapshots store payload under value.*
        if "value" in df.columns:
            df = df.select(col("value.*"))

        # Column guards
        def c(name: str):
            return col(name) if name in df.columns else lit(None)

        # fee column per SQL (used as quantity)
        fee_col = c("fee").cast("double")
        mid_col = c("settle_asset_mid_price").cast("double")

        # Build selection aligned with SQL block
        out = df.select(
            c("user_id").alias("user_id"),
            c("account_id").alias("account_id"),
            lit("crypto").alias("asset_type"),
            lit("USDT").alias("product"),
            lit(10005).alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            # downstream expects asset_sub_type column; set later
            # activity per SQL
            lit("USDT_used_funding_USDT").alias("activity"),
            fee_col.alias("executed_quantity"),
            (-fee_col).alias("net_quantity"),
            c("transaction_time").alias("created"),
            lit("SUCCESS").alias("status"),
            lit(None).cast("string").alias("transaction_type"),
            # partner from config if available else 1000002
            (lit(self.partner_id) if self.partner_id is not None else lit(1000002)).alias("partner_id"),
            lit(None).cast("string").alias("client_id"),
            lit("USDT").alias("currency"),
            mid_col.alias("executed_unit_price"),
            (fee_col * mid_col).alias("executed_total_price"),
            lit(None).alias("fees"),
            lit(1).alias("currency_to_idr"),
            c("transaction_time").alias("transaction_time"),
            c("id").alias("transaction_id"),
        )

        # Attach fields expected by crypto base/common transformations
        out = out.withColumn("asset_id", lit(10005)) \
                 .withColumn("price_info", lit(None)) \
                 .withColumn("taxes_and_fees", lit(None)) \
                 .withColumn("taxation_fee", lit(None)) \
                 .withColumn("usdt_mid_price", col("executed_unit_price")) \
                 .withColumn("gtv", lit(None).cast("double")) \
                 .withColumn("net_gtv", (-col("executed_quantity") * col("executed_unit_price"))) \
                 .withColumn("asset_sub_type", lit("crypto_margin_wallet"))

        return out

    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        if df is None:
            return None
        self.logger.info("=== Applying crypto futures funding specific transformations ===")
        # Enforce SQL-aligned fields post common transformations
        return (
            df.withColumn("activity", lit("USDT_used_funding_USDT"))
              .withColumn("currency", lit("USDT"))
              .withColumn("product", lit("USDT"))
              .withColumn("product_id", lit(10005))
        )


