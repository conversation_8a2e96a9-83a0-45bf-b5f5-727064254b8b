"""
Crypto Futures Transactions Processor
Handles pluang_crypto_futures__crypto_future_transactions joined with instruments when available.
Conservative, column-guarded mapping.
"""

from typing import Optional
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, coalesce, lower, upper, concat, regexp_replace
from .base_crypto_processor import BaseCryptoProcessor


class CryptoFuturesTransactionsProcessor(BaseCryptoProcessor):
    def _get_source_name(self) -> str:
        return "crypto_future_transactions"

    def _get_base_transaction_data(self) -> Optional[DataFrame]:
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")

        # Transactions path
        if self.h_1 == self.t_1:
            tx_path = f"{self.transactions_snapshot_path}{source_name}/dt={self.t_1}/"
        else:
            tx_path = f"{self.transactions_snapshot_path}{source_name}/dt={self.t_1}/hour={self.h_1}/"
        tx = self._read_data_with_error_handling(tx_path, source_name, source_name)
        if tx is None or tx.count() == 0:
            return None
        if "value" in tx.columns:
            tx = tx.select(col("value.*"))

        # Instruments optional join
        instr = None
        instr_path = f"{self.transactions_snapshot_path}crypto_future_instruments/dt={self.t_1}/"
        try:
            instr = self.io_utils.read_json_data(instr_path, True, None, False)
            if instr is not None and "value" in instr.columns:
                instr = instr.select(col("value.*"))
        except Exception:
            instr = None

        if instr is not None and "contract_id" in tx.columns and "id" in instr.columns:
            tx = tx.alias("a").join(instr.alias("b"), col("a.contract_id") == col("b.id"), "left")

        def c(df: DataFrame, name: str):
            return col(name) if name in df.columns else lit(None)

        qty = None
        for candidate in ["executed_quantity", "quantity", "filled_quantity"]:
            if candidate in tx.columns:
                qty = col(candidate)
                break
        if qty is None:
            qty = lit(None)

        unit_price = None
        for candidate in ["executed_unit_price", "unit_price", "price"]:
            if candidate in tx.columns:
                unit_price = col(candidate)
                break
        if unit_price is None:
            unit_price = lit(None)

        total_price = None
        for candidate in ["executed_total_price", "total_price"]:
            if candidate in tx.columns:
                total_price = col(candidate)
                break
        if total_price is None and qty is not None and unit_price is not None:
            total_price = qty * unit_price
        if total_price is None:
            total_price = lit(None)

        side = None
        for candidate in ["side", "transaction_side", "order_side"]:
            if candidate in tx.columns:
                side = col(candidate)
                break

        created = c(tx, "transaction_time") if "transaction_time" in tx.columns else c(tx, "created")

        # Safe net_quantity based on side if present; otherwise fallback to qty
        if side is not None:
            net_qty_col = when(side == lit("BUY"), qty).otherwise(-qty)
        else:
            net_qty_col = qty

        out = tx.select(
            c(tx, "user_id").alias("user_id"),
            c(tx, "account_id").alias("account_id"),
            lit("crypto_futures").alias("asset_type"),
            (c(tx, "contract_symbol") if "contract_symbol" in tx.columns else c(tx, "symbol")).cast("string").alias("product_raw"),
            c(tx, "contract_id").alias("product_id"),
            lit(None).cast("string").alias("contract_id"),
            lit(None).cast("string").alias("contract_name"),
            lit("crypto_futures").alias("asset_sub_type"),
            when(c(tx, "order_type").isNotNull(), lit("transaction_created")).otherwise(lit("transaction")).alias("activity"),
            qty.alias("executed_quantity"),
            net_qty_col.alias("net_quantity"),
            created.alias("created"),
            c(tx, "status").alias("status"),
            (side if side is not None else lit(None)).alias("transaction_type"),
            c(tx, "partner_id").alias("partner_id"),
            c(tx, "client_id").cast("string").alias("client_id"),
            lit("IDR").alias("currency"),
            unit_price.alias("executed_unit_price"),
            total_price.alias("executed_total_price"),
            c(tx, "fee").alias("fees"),
            lit(1).alias("currency_to_idr"),
            c(tx, "transaction_time").alias("transaction_time"),
            c(tx, "id").alias("transaction_id"),
            # keep raw hints for activity/status mapping
            c(tx, "order_type").cast("string").alias("order_type"),
            c(tx, "exchange_status").cast("string").alias("exchange_status"),
            c(tx, "user_status").cast("string").alias("user_status"),
            c(tx, "transaction_type").cast("string").alias("transaction_type_raw"),
        )

        out = out.withColumn("asset_id", lit(None)) \
                 .withColumn("price_info", lit(None)) \
                 .withColumn("taxes_and_fees", lit(None)) \
                 .withColumn("taxation_fee", lit(None))
        return out

    def _apply_processor_specific_transformations(self, df: DataFrame) -> DataFrame:
        # Map status and activity to align with BQ semantics
        if df is None:
            return None

        # Normalize order_type lowercased, default to 'market' when missing
        df = df.withColumn("order_type_norm", lower(coalesce(col("order_type"), lit("market"))))

        # Choose base status from exchange_status -> user_status -> status
        df = df.withColumn(
            "status_base",
            upper(coalesce(col("exchange_status"), col("user_status"), col("status")))
        )

        # Map FILLED -> SUCCESS, NEW/CREATED/INITIATED/PLACED -> PENDING, CANCEL* -> CANCELLED
        df = df.withColumn(
            "status",
            when(col("status_base") == lit("FILLED"), lit("SUCCESS"))
            .when(col("status_base").isin("NEW", "CREATED", "INITIATED", "PLACED"), lit("PENDING"))
            .when(col("status_base").isin("CANCELLED", "CANCELED"), lit("CANCELLED"))
            .otherwise(col("status_base"))
        )

        # Build activity: <order_type>_transaction_<filled|created|cancelled|pending|...>
        df = df.withColumn(
            "activity_suffix",
            when(col("status_base") == lit("FILLED"), lit("filled"))
            .when(col("status_base").isin("NEW", "CREATED"), lit("created"))
            .when(col("status_base").isin("CANCELLED", "CANCELED"), lit("cancelled"))
            .when(col("status_base") == lit("PENDING"), lit("pending"))
            .otherwise(lower(col("status_base")))
        )

        df = df.withColumn(
            "activity",
            concat(col("order_type_norm"), lit("_transaction_"), col("activity_suffix"))
        )

        # Transaction side/type from raw if missing
        df = df.withColumn(
            "transaction_type",
            coalesce(col("transaction_type"), col("transaction_type_raw"))
        )

        # Restore product from raw
        df = df.withColumn("product", coalesce(col("product"), col("product_raw")))

        # Compute net_gtv from sign of net_quantity
        df = df.withColumn(
            "net_gtv",
            when(col("net_quantity").isNotNull() & col("executed_unit_price").isNotNull(),
                 col("net_quantity") * col("executed_unit_price"))
            .otherwise(lit(None).cast("double"))
        )

        # Ensure final asset_type is crypto_futures
        df = df.withColumn("asset_type", lit("crypto_futures"))

        # Cleanup temp columns
        df = df.drop("order_type_norm", "status_base", "activity_suffix", "product_raw", "transaction_type_raw")

        return df


