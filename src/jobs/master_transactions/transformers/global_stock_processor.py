"""
Global Stock Transactions Processor
Handles global stock transactions with FX conversion logic.
Corresponds to SQL lines 3666-3750.
"""

from typing import Dict, List, Any, Optional
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, lit, when, concat, abs as spark_abs, coalesce, get_json_object, lower
from src.utils.custom_logger import get_logger
from src.utils.asset_utils import AssetUtils
from .base_transaction_processor import BaseTransactionProcessor


class GlobalStockProcessor(BaseTransactionProcessor):
    """
    Processor for global stock transactions.
    Handles FX transactions for global stocks with leverage support.
    """
    
    def __init__(self, config: Dict[str, Any], spark, io_utils, ops, t_1: str, h_1: str = None):
        """Initialize GlobalStockProcessor with required dependencies."""
        # Pass t_1 and h_1 as keyword arguments to match base class signature
        super().__init__(config, spark, io_utils, ops, t_1=t_1, h_1=h_1)
        
        # Initialize asset utils for real-time Kafka loading
        self.asset_utils = AssetUtils(spark, config)
        self.logger = get_logger()
    
    def _get_source_name(self) -> str:
        return "global_stock_transactions"
    
    def _get_asset_config(self) -> Dict[str, Any]:
        """Get asset-specific configuration for global stock transactions."""
        from .asset_config import AssetConfigManager
        
        # Get configuration from AssetConfigManager
        config_manager = AssetConfigManager()
        asset_config = config_manager.get_config("global_stocks")
        
        # Convert AssetConfig dataclass to dictionary
        if asset_config:
            config_dict = {
                "asset_type": asset_config.asset_type,
                "asset_sub_type": asset_config.asset_sub_type,
                "leverage": asset_config.leverage,
                "status_filter": asset_config.status_filter,
                "transaction_type_filter": asset_config.transaction_type_filter,
                "partner_id_filter": asset_config.partner_id_filter,
                "transaction_sources": asset_config.transaction_sources,
                "price_join_key": asset_config.price_join_key,
                "currency_conversion_rate": asset_config.currency_conversion_rate,
                "special_transformations": asset_config.special_transformations,
                "dedup_keys": asset_config.dedup_keys,
                "dedup_order_by": asset_config.dedup_order_by,
                "dedup_order_desc": asset_config.dedup_order_desc,
                "source_dedup_config": asset_config.source_dedup_config,
                "effective_spread_rate": asset_config.effective_spread_rate,
                "spread_divisor": asset_config.spread_divisor,
                "buy_tax_rate": asset_config.buy_tax_rate,
                "sell_tax_rate": asset_config.sell_tax_rate
            }
            return config_dict
        else:
            # Fallback configuration if AssetConfigManager fails
            self.logger.warning("Could not load global stocks config from AssetConfigManager, using fallback")
            return {
                "asset_type": "fx",
                "product": "USD",
                "product_id": 10000,
                "currency": "USD",
                "dedup_keys": ["id"],
                "dedup_order_by": "updated",
                "dedup_order_desc": True
            }
    
    def _get_additional_transaction_sources(self) -> List[DataFrame]:
        """Get additional transaction sources for global stock transactions."""
        # Global stock transactions don't have additional sources like crypto
        return []
    
    def _apply_asset_specific_transformations(self, df: DataFrame, crypto_currencies_df: Optional[DataFrame] = None) -> DataFrame:
        """Apply asset-specific transformations to global stock transaction data."""
        # Global stock specific transformations are already applied in _apply_processor_specific_transformations
        return df
    
    def _get_base_transaction_data(self) -> DataFrame:
        """
        Read and prepare base transaction data for global stock transactions.
        Based on SQL lines 25-357 (complete global stock processing logic).
        """
        source_name = self._get_source_name()
        self.logger.info(f"=== Reading {source_name} ===")
        
        try:
            # Construct S3 path
            if self.h_1 == self.t_1:
                s3_path = f"{self.transactions_snapshot_path}global_stock_transactions/dt={self.t_1}/"
                self.logger.info(f"Reading from S3 path (no hour): {s3_path}")
                global_stock_txn = self.io_utils.read_json_data(s3_path, True, None, False)
            else:
                s3_path = f"{self.transactions_snapshot_path}global_stock_transactions/dt={self.t_1}/hour={self.h_1}/"
                self.logger.info(f"Reading from S3 path (with hour): {s3_path}")
                global_stock_txn = self.io_utils.read_json_data(s3_path, True, None, False)
            
            self.logger.info(f"Raw {source_name} count before filtering: {global_stock_txn.count()}")
            
            # Extract data from "value" key if present
            if "value" in global_stock_txn.columns:
                global_stock_txn = global_stock_txn.select(col("value.*"))
                self.logger.info("Extracted data from 'value' key")
            
            # Apply status filter
            global_stock_txn = global_stock_txn.filter(
                col("status").isin(["SUCCESS", "PARTIALLY_FILLED"])
            )
            
            # Add asset_id column early for validation
            global_stock_txn = global_stock_txn.withColumn("asset_id", col("global_stock_id"))
            
            self.logger.info(f"Filtered {source_name} count: {global_stock_txn.count()}")
            return global_stock_txn
            
        except Exception as e:
            error_msg = str(e)
            if "PATH_NOT_FOUND" in error_msg or "Path does not exist" in error_msg:
                self.logger.warning(f"⚠️  Path not found for {source_name}: {error_msg}")
                self.logger.info(f"Creating empty DataFrame with schema for {source_name}")
                
                # Import schema for global stock transactions
                from src.schema.master_transactions_schema import get_global_stock_transactions_schema
                
                # Create empty DataFrame with proper schema
                empty_df = self.spark.createDataFrame([], get_global_stock_transactions_schema())
                
                # Add asset_id column for consistency
                empty_df = empty_df.withColumn("asset_id", col("global_stock_id"))
                
                self.logger.info(f"✅ Created empty DataFrame for {source_name}")
                return empty_df
            else:
                # Re-raise if it's a different error
                self.logger.error(f"❌ Unexpected error reading {source_name}: {error_msg}")
                raise
    
    def _apply_asset_specific_transformations(self, df: DataFrame, crypto_currencies_df: Optional[DataFrame] = None) -> DataFrame:
        """
        Apply transformations specific to global stock transactions.
        Based on complete SQL logic from lines 25-357.
        """
        self.logger.info(f"=== Applying {self._get_source_name()} specific transformations ===")
        
        # Load global stocks lookup data for company_code
        global_stocks_df = self._load_global_stocks_lookup()
        
        # Join with global stocks to get company_code
        if global_stocks_df is not None:
            df = df.join(global_stocks_df, col("global_stock_id") == col("stock_id"), "left")
            self.logger.info("Joined with global stocks lookup data")
        else:
            # Fallback if lookup data is not available
            df = df.withColumn("company_code", lit("UNKNOWN"))
            self.logger.warning("Global stocks lookup data not available, using fallback")
        
        # Add required columns to base DataFrame to ensure they're available throughout the pipeline
        df = df.withColumn("activity", lit("")) \
               .withColumn("asset_type", lit("fx")) \
               .withColumn("product", lit("USD")) \
               .withColumn("product_id", lit(10000)) \
               .withColumn("contract_id", lit("").cast("string")) \
               .withColumn("contract_name", lit("").cast("string")) \
               .withColumn("currency", lit("USD")) \
               .withColumn("asset_subtype", 
                          when(col("stock_type") == "CFD_LEVERAGE", "fx_leverage")
                          .otherwise("fx"))
        
        # Implement dual-query logic: each global stock transaction generates 2 records
        # 1. Global Stock Side (asset_type: "gss", product: company_code)
        # 2. FX Side (asset_type: "fx", product: "USD")
        
        self.logger.info("=== About to start dual-query logic ===")
        self.logger.info(f"DataFrame count before dual-query: {df.count()}")
        self.logger.info("=== Starting dual-query logic ===")
        
        try:
            # Generate Global Stock Side records
            self.logger.info("Calling _generate_global_stock_records...")
            gss_df = self._generate_global_stock_records(df, global_stocks_df)
            gss_count = gss_df.count()
            self.logger.info(f"Generated {gss_count} global stock records")
            
            # Generate FX Side records  
            self.logger.info("Calling _generate_fx_records...")
            fx_df = self._generate_fx_records(df, global_stocks_df)
            fx_count = fx_df.count()
            self.logger.info(f"Generated {fx_count} FX records")
            
            # Union both sides
            self.logger.info("Unioning global stock and FX records...")
            result_df = gss_df.union(fx_df)
            total_count = result_df.count()
            self.logger.info(f"Total records after union: {total_count}")
            
            return result_df
            
        except Exception as e:
            self.logger.error(f"Error in dual-query logic: {str(e)}")
            self.logger.error(f"Error type: {type(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            raise
    
    def _generate_global_stock_records(self, df: DataFrame, global_stocks_df: DataFrame) -> DataFrame:
        """
        Generate Global Stock Side records (asset_type: "gss", product: company_code).
        Based on the first query in the SQL dual-query logic.
        """
        self.logger.info("Generating Global Stock Side records")
        
        # Select and transform for global stock side
        gss_df = df.select(
            col("id").alias("ref_id"),
            col("created"),
            lit("transaction").alias("activity"),
            col("user_id"),
            col("account_id"),
            col("global_stock_id").alias("asset_id"),
            lit("gss").alias("asset_type"),
            col("company_code").alias("product"),
            col("global_stock_id").alias("product_id"),
            lit("").cast("string").alias("contract_id"),
            lit("").cast("string").alias("contract_name"),
            col("stock_type").alias("asset_subtype"),
            lit("USD").alias("currency"),
            col("executed_quantity").alias("quantity"),
            col("executed_quantity").alias("net_quantity"),
            (col("usd_to_idr") * lit(1.0)).alias("unit_price"),
            col("unit_price").alias("unit_price_usd"),
            lit(None).cast("decimal(20,8)").alias("usdt_mid_price"),
            lit(None).cast("decimal(20,8)").alias("underlying_asset_price"),
            (col("executed_total_price") * col("usd_to_idr")).alias("gtv"),
            col("executed_total_price").alias("gtv_usd"),
            (col("executed_total_price") * col("usd_to_idr")).alias("net_gtv"),
            lit("0").alias("trading_margin_usd"),
            lit("0").alias("net_trading_margin_usd"),
            lit(None).cast("decimal(20,8)").alias("spread_revenue"),
            lit(None).cast("decimal(20,8)").alias("external_fees"),
            lit(None).cast("decimal(20,8)").alias("overnight_fee_revenue"),
            lit(None).cast("decimal(20,8)").alias("overnight_fee_revenue_usd"),
            (col("transaction_fee") * col("usd_to_idr")).alias("fee_revenue"),
            (col("transaction_fee") * col("usd_to_idr")).alias("comission_revenue"),
            # Extract exchange fee from transaction_fee_info JSON
            (get_json_object(col("transaction_fee_info"), "$.jfxKbiFee").cast("double") * col("usd_to_idr")).alias("exchange_fee_revenue"),
            lit(None).cast("decimal(20,8)").alias("reg_taf_revenue"),
            lit("0").alias("promo_cost"),
            # Extract tax from transaction_fee_info JSON
            (get_json_object(col("transaction_fee_info"), "$.vat").cast("double") * col("usd_to_idr")).alias("tax_revenue"),
            col("transaction_fee_info").alias("taxes_and_fees"),
            col("realised_gain"),
            col("status"),
            col("transaction_type"),
            col("partner_id"),
            col("client_id"),
            col("recurring_transaction_id"),
            col("user_pocket_id"),
            col("transaction_time")
        )
        
        return gss_df
    
    def _generate_fx_records(self, df: DataFrame, global_stocks_df: DataFrame) -> DataFrame:
        """
        Generate FX Side records (asset_type: "fx", product: "USD").
        Based on the second query in the SQL dual-query logic.
        """
        self.logger.info("Generating FX Side records")
        
        # Select and transform for FX side
        fx_df = df.select(
            col("id").alias("ref_id"),
            col("created"),
            concat(lit("fx_used_gss_"), col("company_code")).alias("activity"),
            col("user_id"),
            col("account_id"),
            lit("10000").alias("asset_id"),  # USD asset_id
            lit("fx").alias("asset_type"),
            lit("USD").alias("product"),
            lit("10000").alias("product_id"),
            lit("").cast("string").alias("contract_id"),
            lit("").cast("string").alias("contract_name"),
            lit("fx").alias("asset_subtype"),
            lit("USD").alias("currency"),
            col("executed_total_price").alias("quantity"),
            (-col("executed_total_price")).alias("net_quantity"),
            col("usd_to_idr").alias("unit_price"),
            lit(None).cast("decimal(20,8)").alias("unit_price_usd"),
            lit(None).cast("decimal(20,8)").alias("usdt_mid_price"),
            lit(None).cast("decimal(20,8)").alias("underlying_asset_price"),
            lit(None).cast("decimal(20,8)").alias("gtv"),
            lit(None).cast("decimal(20,8)").alias("gtv_usd"),
            (-col("executed_total_price") * col("usd_to_idr")).alias("net_gtv"),
            lit(None).cast("decimal(20,8)").alias("trading_margin_usd"),
            lit(None).cast("decimal(20,8)").alias("net_trading_margin_usd"),
            lit(None).cast("decimal(20,8)").alias("spread_revenue"),
            lit(None).cast("decimal(20,8)").alias("external_fees"),
            lit(None).cast("decimal(20,8)").alias("overnight_fee_revenue"),
            lit(None).cast("decimal(20,8)").alias("overnight_fee_revenue_usd"),
            lit(None).cast("decimal(20,8)").alias("fee_revenue"),
            lit(None).cast("decimal(20,8)").alias("comission_revenue"),
            lit(None).cast("decimal(20,8)").alias("exchange_fee_revenue"),
            lit(None).cast("decimal(20,8)").alias("reg_taf_revenue"),
            lit(None).cast("decimal(20,8)").alias("promo_cost"),
            lit(None).cast("decimal(20,8)").alias("tax_revenue"),
            lit("").cast("string").alias("taxes_and_fees"),
            lit(None).cast("decimal(20,8)").alias("realised_gain"),
            col("status"),
            col("transaction_type"),
            col("partner_id"),
            col("client_id"),
            col("recurring_transaction_id"),
            col("user_pocket_id"),
            col("transaction_time")
        )
        
        return fx_df
    
    def process_transactions(self, price_df=None, advanced_price_df=None, conversion_rate=None, crypto_currencies_df=None):
        """
        Override the base process_transactions method to handle global stock specific processing.
        This avoids the column selection issue by handling the entire flow ourselves.
        """
        self.logger.info(f"Processing {self.asset_config['asset_type']} transactions")
        self.logger.info("=== Configuration Values ===")
        self.logger.info(f"transactions_snapshot_path: {self.transactions_snapshot_path}")
        self.logger.info(f"partner_id: {self.partner_id}")
        self.logger.info(f"t_1: {self.t_1}")
        self.logger.info(f"h_1: {self.h_1}")
        self.logger.info(f"asset_type: {self.asset_config['asset_type']}")
        self.logger.info(f"DEBUG: Full asset_config = {self.asset_config}")
        
        # Step 1: Get base transaction data
        df = self._get_base_transaction_data()
        if df is None:
            self.logger.warning(f"No base transaction data found for {self.asset_config['asset_type']}")
            return None
        
        # Check if DataFrame is empty
        row_count = df.count()
        if row_count == 0:
            self.logger.warning(f"Empty DataFrame returned for {self.asset_config['asset_type']}")
            self.logger.info("Returning None - no data to process")
            return None
        
        # Apply deduplication before processing
        df = self._apply_deduplication(df)
        df.show(5, truncate=False)
        self.logger.info(f"Final processed {row_count} {self.asset_config['asset_type']} transactions")
        
        # Step 2: Get additional transaction sources and union them
        additional_sources = self._get_additional_transaction_sources()
        for source_df in additional_sources:
            df = self.ops.get_union(df, source_df)
        
        # Step 3: Apply common transformations
        df = self._apply_common_transformations(df)
        
        # Step 4: Apply dual-query transformations (Global Stock + FX sides)
        df = self._apply_asset_specific_transformations(df, crypto_currencies_df)
        
        # Skip steps 5-7 as our dual-query logic handles all necessary transformations
        # Step 5: Join with prices - SKIPPED (handled in dual-query)
        # Step 6: Add currency conversion - SKIPPED (handled in dual-query) 
        # Step 7: Validate data - SKIPPED (our dual-query methods create clean data)
        
        # Step 8: Return the final DataFrame
        self.logger.info(f"Processed {df.count()} {self.asset_config['asset_type']} transactions")
        return df
    
    def _get_common_columns(self) -> List[str]:
        """
        Override to return global stock specific columns.
        Global stocks have a different schema than other assets.
        """
        return [
            "ref_id", "created", "activity", "user_id", "account_id", "asset_id",
            "asset_type", "product", "product_id", "contract_id", "contract_name",
            "asset_subtype", "currency", "executed_quantity", "net_quantity",
            "unit_price", "usd_to_idr", "gtv", "net_gtv",
            "transaction_type", "status", "partner_id", "client_id", "transaction_time",
            "executed_total_price", "total_price", "effective_spread", "raw_spread",
            "exchange_mid_price", "transaction_fee", "trading_margin_used",
            "recurring_transaction_id", "user_pocket_id", "realised_gain",
            "transaction_fee_info", "order_type", "stock_type", "hedging_type",
            "quantity"
        ]
    
    def _process_limit_transactions(self, df: DataFrame) -> DataFrame:
        """Process limit transactions (test_limit + limit_success logic)."""
        self.logger.info("Processing limit transactions")
        
        # Filter for limit orders with SUCCESS/PARTIALLY_FILLED status
        limit_df = df.filter(
            (col("order_type") == "LIMIT") & 
            (col("status").isin(["SUCCESS", "PARTIALLY_FILLED"]))
        )
        
        # Apply activity logic
        limit_df = limit_df.withColumn("activity",
            concat(lit("limit_transaction_filled"),
                   when(col("recurring_transaction_id").isNotNull(), lit("_recurring"))
                   .when(col("trading_hours").isin(["INCLUDE_PRE_MARKET", "INCLUDE_EXTENDED_HOURS"]), lit("_premarket"))
                   .otherwise(lit("")))
        )
        
        # Set created field with transaction_time migration logic
        limit_df = limit_df.withColumn("created",
            when(col("transaction_time").isNull(), col("updated"))
            .otherwise(col("transaction_time"))
        )
        
        # Ensure all required columns are present
        return limit_df
    
    def _process_other_transactions(self, df: DataFrame) -> DataFrame:
        """Process other transactions (market orders, etc.)."""
        self.logger.info("Processing other transactions")
        
        # Filter for non-limit orders or limit orders after 2022-04-12
        other_df = df.filter(
            (col("order_type") != "LIMIT") | 
            (col("order_type") == "LIMIT" & col("updated") >= "2022-04-12")
        )
        
        # Apply activity logic based on order_type
        other_df = other_df.withColumn("activity",
            when(col("order_type") == "MARKET", 
                 concat(lit("transaction"),
                        when(col("recurring_transaction_id").isNotNull(), lit("_recurring"))
                        .when(col("trading_hours").isin(["INCLUDE_PRE_MARKET", "INCLUDE_EXTENDED_HOURS"]), lit("_premarket"))
                        .otherwise(lit(""))))
            .when(col("order_type").isin(["OCO", "STOP", "STOP_LIMIT"]),
                 concat(lower(coalesce(col("info.exitStrategy"), col("order_type"))), lit("_transaction_created"),
                        when(col("recurring_transaction_id").isNotNull(), lit("_recurring"))
                        .when(col("trading_hours").isin(["INCLUDE_PRE_MARKET", "INCLUDE_EXTENDED_HOURS"]), lit("_premarket"))
                        .otherwise(lit(""))))
            .when(col("order_type") == "LIMIT",
                 concat(lit("limit_transaction_created"),
                        when(col("recurring_transaction_id").isNotNull(), lit("_recurring"))
                        .when(col("trading_hours").isin(["INCLUDE_PRE_MARKET", "INCLUDE_EXTENDED_HOURS"]), lit("_premarket"))
                        .otherwise(lit(""))))
        )
        
        # Set created field
        other_df = other_df.withColumn("created",
            when(col("order_type") == "LIMIT", col("created"))
            .otherwise(coalesce(col("transaction_time"), col("updated")))
        )
        
        return other_df
    
    def _process_limit_cancels(self, df: DataFrame) -> DataFrame:
        """Process cancelled limit transactions."""
        self.logger.info("Processing limit cancels")
        
        # Filter for cancelled non-market orders
        cancel_df = df.filter(
            (col("order_type") != "MARKET") & 
            (col("status") == "CANCELLED")
        )
        
        # Apply activity logic
        cancel_df = cancel_df.withColumn("activity",
            when(col("order_type") == "LIMIT",
                 concat(lit("limit_transaction_cancelled"),
                        when(col("recurring_transaction_id").isNotNull(), lit("_recurring"))
                        .when(col("trading_hours").isin(["INCLUDE_PRE_MARKET", "INCLUDE_EXTENDED_HOURS"]), lit("_premarket"))
                        .otherwise(lit(""))))
            .when(col("order_type").isin(["OCO", "STOP", "STOP_LIMIT"]),
                 concat(lower(coalesce(col("info.exitStrategy"), col("order_type"))), lit("_transaction_cancelled"),
                        when(col("recurring_transaction_id").isNotNull(), lit("_recurring"))
                        .when(col("trading_hours").isin(["INCLUDE_PRE_MARKET", "INCLUDE_EXTENDED_HOURS"]), lit("_premarket"))
                        .otherwise(lit(""))))
        )
        
        # Set created field
        cancel_df = cancel_df.withColumn("created",
            when(col("transaction_time").isNull(), col("updated"))
            .otherwise(col("transaction_time"))
        )
        
        return cancel_df
    
    def _apply_final_transformations(self, df: DataFrame) -> DataFrame:
        """Apply final transformations to the unioned data."""
        self.logger.info("Applying final transformations")
        
        # Select and transform columns to match expected schema
        result_df = df.select(
            col("id").alias("ref_id"),
            col("created"),
            col("activity"),
            col("user_id"),
            col("account_id"),
            col("global_stock_id"),
            col("order_type"),
            col("transaction_type"),
            col("stock_type"),
            col("hedging_type"),
            col("status"),
            coalesce(col("executed_total_price"), col("total_price")).alias("executed_total_price"),
            coalesce(col("unit_price"), col("unit_price")).alias("executed_unit_price"),
            coalesce(col("executed_quantity"), col("quantity")).alias("executed_quantity"),
            col("effective_spread"),
            col("raw_spread"),
            col("exchange_mid_price"),
            col("transaction_fee"),
            col("usd_to_idr"),
            col("partner_id"),
            col("client_id"),
            col("trading_margin_used"),
            col("recurring_transaction_id"),
            col("user_pocket_id"),
            col("total_price"),
            col("realised_gain"),
            col("transaction_fee_info"),
            col("transaction_time")
        ) \
         .withColumn("asset_id", col("global_stock_id")) \
         .withColumn("unit_price", col("usd_to_idr")) \
         .withColumn("unit_price_usd", lit(None).cast("decimal(20,8)")) \
         .withColumn("usdt_mid_price", lit(None).cast("decimal(20,8)")) \
         .withColumn("underlying_asset_price", lit(None).cast("decimal(20,8)")) \
         .withColumn("gtv", lit(None).cast("decimal(20,8)")) \
         .withColumn("gtv_usd", lit(None).cast("decimal(20,8)")) \
         .withColumn("trading_margin_usd", lit(None).cast("decimal(20,8)")) \
         .withColumn("net_trading_margin_usd", lit(None).cast("decimal(20,8)")) \
         .withColumn("external_fees", lit(None).cast("decimal(20,8)")) \
         .withColumn("overnight_fee_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("overnight_fee_revenue_usd", lit(None).cast("decimal(20,8)")) \
         .withColumn("spread_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("fee_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("commission_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("exchange_fee_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("reg_taf_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("promo_cost", lit(None).cast("decimal(20,8)")) \
         .withColumn("tax_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("spread_cost", lit(None).cast("decimal(20,8)")) \
         .withColumn("rounding_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("fx_spread_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("installment_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("downpayment_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("penalty_revenue", lit(None).cast("decimal(20,8)")) \
         .withColumn("idr2usd_ref_id", lit("").cast("string")) \
         .withColumn("usd2usdmargin_ref_id", lit("").cast("string")) \
         .withColumn("usdmargin2usd_ref_id", lit("").cast("string")) \
         .withColumn("prorated_hedge_pnl", lit(None).cast("decimal(20,8)")) \
         .withColumn("prorated_hedge_pnl_usd", lit(None).cast("decimal(20,8)")) \
         .withColumn("partner_commission", lit(None).cast("decimal(20,8)")) \
         .withColumn("partner_commission_pct", lit(None).cast("decimal(20,8)")) \
         .withColumn("broker_fee", lit(None).cast("decimal(20,8)")) \
         .withColumn("broker_fee_tax", lit(None).cast("decimal(20,8)")) \
         .withColumn("market_maker_fee", lit(None).cast("decimal(20,8)")) \
         .withColumn("market_maker_fee_tax", lit(None).cast("decimal(20,8)")) \
         .withColumn("ref_id_hedge", lit("").cast("string")) \
         .withColumn("network", lit("").cast("string")) \
         .withColumn("taxes_and_fees", lit("").cast("string")) \
         .withColumn("realised_gain_native", lit(None).cast("decimal(20,8)")) \
         .withColumn("is_liquidation", lit(False).cast("boolean"))
        
        # Add quantity and net_quantity calculations based on SQL logic
        result_df = result_df.withColumn("quantity",
            when((col("transaction_type") == "BUY") & (col("stock_type") == "CFD_LEVERAGE"), 
                 coalesce(col("transaction_fee"), lit(0)))
            .when((col("transaction_type") == "SELL") & (col("stock_type") == "CFD_LEVERAGE"), 
                 spark_abs(coalesce(col("realised_gain"), lit(0)) - coalesce(col("transaction_fee"), lit(0))))
            .when((col("transaction_type") == "BUY") & (col("stock_type") != "CFD_LEVERAGE"), 
                 coalesce(col("executed_total_price"), lit(0)) + coalesce(col("transaction_fee"), lit(0)))
            .when((col("transaction_type") == "SELL") & (col("stock_type") != "CFD_LEVERAGE"), 
                 coalesce(col("executed_total_price"), lit(0)) - coalesce(col("transaction_fee"), lit(0)))
            .otherwise(lit("").cast("string"))
        )
        
        result_df = result_df.withColumn("net_quantity",
            when((col("transaction_type") == "BUY") & (col("stock_type") == "CFD_LEVERAGE"), 
                 -coalesce(col("transaction_fee"), lit(0)))
            .when((col("transaction_type") == "SELL") & (col("stock_type") == "CFD_LEVERAGE"), 
                 coalesce(col("realised_gain"), lit(0)) - coalesce(col("transaction_fee"), lit(0)))
            .when((col("transaction_type") == "BUY") & (col("stock_type") != "CFD_LEVERAGE"), 
                 -coalesce(col("executed_total_price"), lit(0)) - coalesce(col("transaction_fee"), lit(0)))
            .when((col("transaction_type") == "SELL") & (col("stock_type") != "CFD_LEVERAGE"), 
                 coalesce(col("executed_total_price"), lit(0)) - coalesce(col("transaction_fee"), lit(0)))
            .otherwise(lit("").cast("string"))
        )
        
        # Add net_gtv calculation
        result_df = result_df.withColumn("net_gtv",
            when((col("transaction_type") == "BUY") & (col("stock_type") == "CFD_LEVERAGE"), 
                 (-coalesce(col("transaction_fee"), lit(0))) * col("usd_to_idr"))
            .when((col("transaction_type") == "SELL") & (col("stock_type") == "CFD_LEVERAGE"), 
                 (coalesce(col("realised_gain"), lit(0)) - coalesce(col("transaction_fee"), lit(0))) * col("usd_to_idr"))
            .when((col("transaction_type") == "BUY") & (col("stock_type") != "CFD_LEVERAGE"), 
                 (-coalesce(col("executed_total_price"), lit(0)) - coalesce(col("transaction_fee"), lit(0))) * col("usd_to_idr"))
            .when((col("transaction_type") == "SELL") & (col("stock_type") != "CFD_LEVERAGE"), 
                 (coalesce(col("executed_total_price"), lit(0)) - coalesce(col("transaction_fee"), lit(0))) * col("usd_to_idr"))
            .otherwise(lit("").cast("string"))
        )
        
        return result_df
    
    def _load_global_stocks_lookup(self) -> DataFrame:
        """
        Load global stocks lookup data to get company_code.
        Uses AssetUtils to get data from Kafka with S3 fallback.
        """
        try:
            self.logger.info("Loading global stocks lookup using AssetUtils from Kafka")
            
            # Use AssetUtils to get global stock assets from Kafka
            lookup_df = self.asset_utils.get_global_stock_assets()
            
            if lookup_df is not None and lookup_df.count() > 0:
                # Rename columns for compatibility with existing join logic
                # AssetUtils.get_global_stock_assets() returns exploded data with all fields
                lookup_df = lookup_df.select(
                    col("id").alias("stock_id"),                 # For joining
                    col("id").alias("id"),                       # Alternative join key
                    col("company_code").alias("company_code"),   # Main lookup field
                    col("symbol").alias("symbol"),               # Stock symbol
                    col("name").alias("name"),                   # Company name
                    col("active").alias("active")                # Active status
                ).filter(col("active") == True)                 # Only active stocks
                
                self.logger.info(f"✅ Loaded {lookup_df.count()} global stocks for lookup from Kafka")
                return lookup_df
            else:
                self.logger.warning("⚠️  No global stocks lookup data found from Kafka")
                # Fallback to S3 method
                return self._load_global_stocks_lookup_fallback()
                
        except Exception as e:
            self.logger.error(f"❌ Error loading global stocks lookup from Kafka: {str(e)}")
            # Fallback to original S3 method if Kafka fails
            return self._load_global_stocks_lookup_fallback()
    
    def _load_global_stocks_lookup_fallback(self) -> DataFrame:
        """
        Fallback method to load global stocks lookup from S3 directly.
        Used when Kafka loader fails completely.
        """
        try:
            lookup_path = f"{self.transactions_snapshot_path}global_stocks/dt={self.t_1}/"
            self.logger.info(f"Loading global stocks lookup from S3 fallback: {lookup_path}")
            
            # Read the JSON file (flat JSON, no "value" wrapper)
            lookup_df = self.io_utils.read_json_data(lookup_path, False, None, False)
            
            if lookup_df is not None and lookup_df.count() > 0:
                # Rename id to stock_id for joining
                lookup_df = lookup_df.withColumnRenamed("id", "stock_id")
                self.logger.info(f"✅ Loaded {lookup_df.count()} global stocks for lookup from S3 fallback")
                return lookup_df
            else:
                self.logger.warning("⚠️  No global stocks lookup data found in S3 fallback")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error loading global stocks lookup from S3 fallback: {str(e)}")
            return None
