"""
EMR Job Wrapper for Global Stock Transaction Processing
Provides a job wrapper class that can be instantiated by master_transaction_main.py dispatcher.
"""

from typing import Dict, Any
from src.utils.config_loader import ConfigLoader
from src.utils.spark_utils import SparkUtils


class GlobalStockTransactionJob:
    """
    EMR-compatible job wrapper for global stock transaction processing.
    This class follows the expected pattern for the job dispatcher.
    Instantiated by master_transaction_main.py with config and kwargs.
    """
    
    def __init__(self, config: Dict[str, Any], **kwargs):
        """Initialize the global stock transaction job with config and kwargs."""
        # Load master transaction specific configuration using ConfigLoader
        config_loader = ConfigLoader("aws")
        master_config = config_loader.load_master_transaction_config()

        # Merge with the generic config, giving priority to master transaction config
        self.config = {**config, **master_config}
        self.kwargs = kwargs
        
        # Extract common parameters from config (set by main.py) or kwargs (for direct calls)
        self.t_1 = self.config.get('t_1') or kwargs.get('t_1')
        self.h_1 = self.config.get('h_1') or kwargs.get('h_1') or self.t_1
        
        # Create Spark session using SparkUtils pattern
        self.spark_utils = SparkUtils("GlobalStockTransactionJob")
        self.spark = self.spark_utils.create_spark_session()
        print("✅ Spark session created for GlobalStockTransactionJob")
        print(f"✅ Configuration loaded")
    
    def execute(self):
        """Main execution method for global stock processing."""
        print("🚀 Starting Global Stock Transaction Processing")
        print(f"Processing date: {self.t_1}")
        print(f"Processing hour: {self.h_1}")
        
        try:
            # Import required modules
            from src.utils.io_utils import IOUtils
            from src.utils.operations import Operations
            from src.jobs.master_transactions.transformers.global_stock_processor import GlobalStockProcessor
            
            # Initialize utilities
            io_utils = IOUtils(self.spark, self.config)
            ops = Operations(self.spark)
            
            # Create global stock processor
            processor = GlobalStockProcessor(
                config=self.config,
                spark=self.spark,
                io_utils=io_utils,
                ops=ops,
                t_1=self.t_1,
                h_1=self.h_1
            )
            
            # Process global stock transactions
            print("=== Processing global stock transactions ===")
            result_df = processor.process_transactions()
            
            if result_df is None:
                print("⚠️  No global stock data found - path may not exist or data is empty")
                print("✅ Job completed successfully with no data to process")
                return
            
            row_count = result_df.count()
            if row_count > 0:
                # Write results to S3
                bucket = self.config.get("bucket_name", "pluang-datalake-calculated-staging")
                output_base = self.config.get("master_transaction_path", f"s3a://{bucket_name}/processed_data/master_transactions/")
                output_path = f"{output_base}dt={self.t_1}/"
                if self.h_1 and self.h_1 != self.t_1:
                    output_path += f"hour={self.h_1}/"
                
                print(f"Writing global stock results to: {output_path}")
                result_df.coalesce(10).write.mode("overwrite").parquet(output_path)
                
                print(f"✅ Successfully processed {row_count} global stock transactions")
            else:
                print("⚠️  No global stock data to write - DataFrame is empty")
                print("✅ Job completed successfully with no data to process")
                
        except Exception as e:
            print(f"❌ Global stock processing failed: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def run(self):
        """Run method for compatibility with job dispatcher."""
        try:
            self.execute()
        finally:
            if hasattr(self, 'spark_utils'):
                self.spark_utils.stop_spark(self.spark)
        
        print("🏁 Global stock transaction processing completed")

