"""
Data validator service implementation.
"""

import time
import os
from typing import <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>
from pyspark.sql import <PERSON><PERSON><PERSON><PERSON>, SparkSession
from pyspark.sql.functions import col, isnan
from ..interfaces.data_validator import DataValidator
from ..models.validation_config import ValidationConfig
from ..exceptions.validation_exception import ValidationException
from src.utils.custom_logger import get_logger        



class DataValidatorService(DataValidator):
    """
    Service for validating data between different datasets.
    
    This service implements the DataValidator interface and provides
    comprehensive validation capabilities including data quality checks,
    join key validation, and missing data analysis.
    """
    
    def __init__(self, logger, config, spark_session: SparkSession):
        """
        Initialize the data validator service.
        
        Args:
            spark_session: Active Spark session
        """
        self.spark = spark_session
        self.config = config
        self.logger = logger
    
    def validate_data(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: <PERSON>ple[str, str],
        validation_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Perform comprehensive validation between two DataFrames.
        
        Args:
            df1: First DataFrame for comparison
            df2: Second DataFrame for comparison
            join_keys: Tuple of (join_key1, join_key2) column names
            validation_config: Configuration dictionary for validation options
            
        Returns:
            Dictionary containing validation results
        """
        try:
            self.logger.info("Starting comprehensive data validation")
            start_time = time.time()
            
            # Parse validation configuration
            config = ValidationConfig.from_dict(validation_config)
            
            # Validate join keys first
            join_key_validation = self.validate_join_keys(df1, df2, join_keys)
            if not join_key_validation['is_valid']:
                self.logger.error(f"Join key validation failed: {join_key_validation['errors']}")
                raise ValidationException(
                    "Join key validation failed",
                    details={'join_key_validation': join_key_validation}
                )
            
            # Perform validation based on configuration
            results = {}
            
            # Basic join operations
            join_key1, join_key2 = join_keys
            
            # Debug: Check for duplicate values in join keys
            df1_join_key_distinct = df1.select(join_key1).distinct().count()
            df2_join_key_distinct = df2.select(join_key2).distinct().count()
            df1_total = df1.count()
            df2_total = df2.count()
            
            self.logger.info(f"Join key analysis:")
            self.logger.info(f"  File 1: {df1_total} total records, {df1_join_key_distinct} distinct {join_key1} values")
            self.logger.info(f"  File 2: {df2_total} total records, {df2_join_key_distinct} distinct {join_key2} values")
            
            if df1_join_key_distinct < df1_total:
                self.logger.warning(f"  File 1 has duplicate {join_key1} values - this may cause join multiplication")
            if df2_join_key_distinct < df2_total:
                self.logger.warning(f"  File 2 has duplicate {join_key2} values - this may cause join multiplication")
            
            # Rename columns to avoid ambiguity during join
            df1_renamed = df1.select([col(c).alias(f"df1_{c}") for c in df1.columns])
            df2_renamed = df2.select([col(c).alias(f"df2_{c}") for c in df2.columns])
            
            # INNER JOIN for matched records (records that exist in both datasets)
            matched_df = df1_renamed.join(
                df2_renamed, 
                df1_renamed[f"df1_{join_key1}"] == df2_renamed[f"df2_{join_key2}"], 
                'inner'
            )
            
            # Debug: Check join results
            matched_count = matched_df.count()
            self.logger.info(f"  Join result: {matched_count} matched records")
            
            # LEFT ANTI JOIN for records in df1 but not in df2
            missing_in_df2_df = df1_renamed.join(
                df2_renamed, 
                df1_renamed[f"df1_{join_key1}"] == df2_renamed[f"df2_{join_key2}"], 
                'left_anti'
            )
            
            # LEFT ANTI JOIN for records in df2 but not in df1
            missing_in_df1_df = df2_renamed.join(
                df1_renamed, 
                df2_renamed[f"df2_{join_key2}"] == df1_renamed[f"df1_{join_key1}"], 
                'left_anti'
            )
            
            # Store results
            results['matched_records'] = matched_df
            results['missing_in_df1'] = missing_in_df1_df
            results['missing_in_df2'] = missing_in_df2_df
            
            # Unmatched records (records that exist in both but don't match on other criteria)
            if config.include_data_quality:
                results['unmatched_records'] = self._identify_unmatched_records(
                    results['matched_records'], join_keys
                )
            else:
                results['unmatched_records'] = self.spark.createDataFrame([], matched_df.schema)
            
            # Generate summary
            results['summary'] = self._generate_summary(df1, df2, results)
            
            # Data quality metrics
            if config.include_data_quality:
                results['quality_metrics'] = self._generate_quality_metrics(df1, df2, join_keys)
            
            # Processing time
            processing_time = time.time() - start_time
            results['processing_time'] = processing_time
            
            self.logger.info(f"Validation completed in {processing_time:.2f} seconds")
            
            return results
            
        except Exception as e:
            if isinstance(e, ValidationException):
                raise
            raise ValidationException(
                f"Data validation failed: {str(e)}",
                details={'error': str(e)}
            )
    
    def validate_join_keys(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str]
    ) -> Dict[str, Any]:
        """
        Validate the join keys for data integrity.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            join_keys: Tuple of (join_key1, join_key2) column names
            
        Returns:
            Dictionary containing join key validation results
        """
        try:
            join_key1, join_key2 = join_keys
            warnings = []
            errors = []
            
            # Check if join keys exist in both DataFrames
            if join_key1 not in df1.columns:
                errors.append(f"Join key '{join_key1}' not found in first DataFrame")
            if join_key2 not in df2.columns:
                errors.append(f"Join key '{join_key2}' not found in second DataFrame")
            
            if errors:
                return {
                    'is_valid': False,
                    'warnings': warnings,
                    'errors': errors
                }
            
            # Check for null values in join keys
            null_count_df1 = df1.filter(col(join_key1).isNull() | isnan(col(join_key1))).count()
            null_count_df2 = df2.filter(col(join_key2).isNull() | isnan(col(join_key2))).count()
            
            if null_count_df1 > 0:
                warnings.append(f"First DataFrame has {null_count_df1} null values in join key '{join_key1}'")
            if null_count_df2 > 0:
                warnings.append(f"Second DataFrame has {null_count_df2} null values in join key '{join_key2}'")
            
            # Check for duplicate values in join keys
            duplicate_count_df1 = df1.groupBy(join_key1).count().filter(col('count') > 1).count()
            duplicate_count_df2 = df2.groupBy(join_key2).count().filter(col('count') > 1).count()
            
            if duplicate_count_df1 > 0:
                warnings.append(f"First DataFrame has {duplicate_count_df1} duplicate values in join key '{join_key1}'")
            if duplicate_count_df2 > 0:
                warnings.append(f"Second DataFrame has {duplicate_count_df2} duplicate values in join key '{join_key2}'")
            
            return {
                'is_valid': True,
                'warnings': warnings,
                'errors': errors
            }
            
        except Exception as e:
            return {
                'is_valid': False,
                'warnings': [],
                'errors': [f"Join key validation error: {str(e)}"]
            }
    
    def analyze_data_quality(
        self, 
        df: DataFrame, 
        column_name: str
    ) -> Dict[str, Any]:
        """
        Analyze data quality for a specific column.
        
        Args:
            df: DataFrame to analyze
            column_name: Name of the column to analyze
            
        Returns:
            Dictionary containing quality metrics
        """
        try:
            if column_name not in df.columns:
                return {
                    'null_count': 0,
                    'unique_count': 0,
                    'duplicate_count': 0,
                    'data_types': 'Column not found'
                }
            
            # Count null values
            null_count = df.filter(
                col(column_name).isNull() | isnan(col(column_name))
            ).count()
            
            # Count unique values
            unique_count = df.select(column_name).distinct().count()
            
            # Count duplicate values
            total_count = df.count()
            duplicate_count = total_count - unique_count
            
            # Get data type
            data_type = str(df.schema[column_name].dataType)
            
            return {
                'null_count': null_count,
                'unique_count': unique_count,
                'duplicate_count': duplicate_count,
                'data_types': data_type,
                'total_count': total_count
            }
            
        except Exception as e:
            self.logger.warning(f"Data quality analysis failed for column {column_name}: {e}")
            return {
                'null_count': 0,
                'unique_count': 0,
                'duplicate_count': 0,
                'data_types': 'Analysis failed'
            }
    
    def detect_missing_data(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str]
    ) -> Dict[str, DataFrame]:
        """
        Detect missing data between two datasets.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            join_keys: Tuple of (join_key1, join_key2) column names
            
        Returns:
            Dictionary containing missing data DataFrames
        """
        try:
            join_key1, join_key2 = join_keys
            
            # Find records in df1 that are not in df2
            missing_in_df2 = df1.join(
                df2, 
                df1[join_key1] == df2[join_key2], 
                'left_anti'
            )
            
            # Find records in df2 that are not in df1
            missing_in_df1 = df2.join(
                df1, 
                df2[join_key2] == df1[join_key1], 
                'left_anti'
            )
            
            return {
                'missing_in_df1': missing_in_df1,
                'missing_in_df2': missing_in_df2
            }
            
        except Exception as e:
            self.logger.error(f"Missing data detection failed: {e}")
            # Return empty DataFrames on error
            empty_df = self.spark.createDataFrame([], df1.schema)
            return {
                'missing_in_df1': empty_df,
                'missing_in_df2': empty_df
            }
    
    def _identify_unmatched_records(
        self, 
        matched_df: DataFrame, 
        join_keys: Tuple[str, str]  # pylint: disable=unused-argument
    ) -> DataFrame:
        """
        Identify records that match on join keys but differ on other criteria.
        
        Args:
            matched_df: DataFrame of matched records
            join_keys: Tuple of (join_key1, join_key2) column names
            
        Returns:
            DataFrame of unmatched records
        """
        try:
            # This is a simplified implementation
            # In a real scenario, you might want to compare specific columns
            # or implement more sophisticated matching logic
            
            # For now, return empty DataFrame
            return self.spark.createDataFrame([], matched_df.schema)
            
        except Exception as e:
            self.logger.warning(f"Unmatched records identification failed: {e}")
            return self.spark.createDataFrame([], matched_df.schema)
    
    def _generate_summary(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        results: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate validation summary statistics.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            results: Validation results
            
        Returns:
            Dictionary containing summary statistics
        """
        try:
            total_records_df1 = df1.count()
            total_records_df2 = df2.count()
            
            matched_count = results['matched_records'].count()
            missing_in_df1_count = results['missing_in_df1'].count()
            missing_in_df2_count = results['missing_in_df2'].count()
            unmatched_count = results['unmatched_records'].count()
            
            # Calculate realistic metrics
            total_records = total_records_df1 + total_records_df2
            total_missing = missing_in_df1_count + missing_in_df2_count
            
            # Match percentage should be based on the smaller dataset (realistic expectation)
            min_dataset_size = min(total_records_df1, total_records_df2)
            match_percentage = (matched_count / min_dataset_size * 100) if min_dataset_size > 0 else 0
            
            # Coverage percentage (how much of the smaller dataset is covered)
            coverage_percentage = (matched_count / min_dataset_size * 100) if min_dataset_size > 0 else 0
            
            return {
                'total_records_df1': total_records_df1,
                'total_records_df2': total_records_df2,
                'total_records': total_records,
                'matched_count': matched_count,
                'missing_in_df1_count': missing_in_df1_count,
                'missing_in_df2_count': missing_in_df2_count,
                'total_missing': total_missing,
                'unmatched_count': unmatched_count,
                'match_percentage': match_percentage,
                'coverage_percentage': coverage_percentage,
                'match_percentage_df1': (matched_count / total_records_df1 * 100) if total_records_df1 > 0 else 0,
                'match_percentage_df2': (matched_count / total_records_df2 * 100) if total_records_df2 > 0 else 0
            }
            
        except Exception as e:
            self.logger.warning(f"Summary generation failed: {e}")
            return {
                'total_records_df1': 0,
                'total_records_df2': 0,
                'total_records': 0,
                'matched_count': 0,
                'missing_in_df1_count': 0,
                'missing_in_df2_count': 0,
                'total_missing': 0,
                'unmatched_count': 0,
                'match_percentage': 0,
                'coverage_percentage': 0,
                'match_percentage_df1': 0,
                'match_percentage_df2': 0
            }
    
    def _generate_quality_metrics(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str]
    ) -> Dict[str, Any]:
        """
        Generate data quality metrics.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            join_keys: Tuple of (join_key1, join_key2) column names
            
        Returns:
            Dictionary containing quality metrics
        """
        try:
            join_key1, join_key2 = join_keys
            
            # Analyze join key quality
            df1_join_quality = self.analyze_data_quality(df1, join_key1)
            df2_join_quality = self.analyze_data_quality(df2, join_key2)
            
            return {
                'df1_join_key_quality': df1_join_quality,
                'df2_join_key_quality': df2_join_quality,
                'overall_quality_score': self._calculate_quality_score(df1_join_quality, df2_join_quality)
            }
            
        except Exception as e:
            self.logger.warning(f"Quality metrics generation failed: {e}")
            return {}
    
    def _calculate_quality_score(
        self, 
        quality1: Dict[str, Any], 
        quality2: Dict[str, Any]
    ) -> float:
        """
        Calculate overall quality score.
        
        Args:
            quality1: Quality metrics for first DataFrame
            quality2: Quality metrics for second DataFrame
            
        Returns:
            Quality score between 0 and 1
        """
        try:
            # Simple quality scoring based on null and duplicate percentages
            total1 = quality1.get('total_count', 1)
            total2 = quality2.get('total_count', 1)
            
            null_score1 = 1 - (quality1.get('null_count', 0) / total1)
            null_score2 = 1 - (quality2.get('null_count', 0) / total2)
            
            duplicate_score1 = 1 - (quality1.get('duplicate_count', 0) / total1)
            duplicate_score2 = 1 - (quality2.get('duplicate_count', 0) / total2)
            
            # Average the scores
            avg_score = (null_score1 + null_score2 + duplicate_score1 + duplicate_score2) / 4
            
            return max(0.0, min(1.0, avg_score))
            
        except Exception:
            return 0.0
    
    def estimate_processing_time(self, file1_path: str, file2_path: str) -> Dict[str, Any]:
        """Estimate processing time for the validation."""
        try:
            # Simple estimation based on file existence and basic size
            file1_size = self._get_file_size_mb(file1_path)
            file2_size = self._get_file_size_mb(file2_path)
            total_size_mb = file1_size + file2_size
            
            # Rough estimation: 0.1 seconds per MB
            estimated_time = total_size_mb * 0.1
            
            recommendations = []
            if total_size_mb > 1000:
                recommendations.append("Large files detected - consider running during off-peak hours")
            if total_size_mb > 100:
                recommendations.append("Ensure sufficient disk space for output")
            
            return {
                'estimated_time_seconds': estimated_time,
                'estimated_time_minutes': estimated_time / 60,
                'total_size_mb': total_size_mb,
                'file1_size_mb': file1_size,
                'file2_size_mb': file2_size,
                'recommendations': recommendations
            }
        except Exception as e:
            self.logger.exception("Processing time estimation failed: %s", e)
            return {
                'estimated_time_seconds': 0,
                'estimated_time_minutes': 0,
                'total_size_mb': 0,
                'recommendations': ['Unable to estimate processing time']
            }
    
    def _get_file_size_mb(self, file_path: str) -> float:
        """Get file size in MB (simplified estimation)."""
        try:
            if file_path.startswith('s3a://'):
                # For S3 files, return a default estimate
                return 10.0
            else:
                if os.path.exists(file_path):
                    return os.path.getsize(file_path) / (1024 * 1024)
                return 0.0
        except Exception:
            return 0.0
    
    def perform_validation(
        self, 
        df1: DataFrame, 
        df2: DataFrame, 
        join_keys: Tuple[str, str],
        validation_config: Dict[str, Any],
        file1_path: str = None,
        file2_path: str = None
    ) -> Dict[str, Any]:
        """Perform the core validation workflow."""
        start_time = time.time()
        
        try:
            self.logger.info("Starting validation workflow...")
            if file1_path:
                self.logger.info("File 1: %s", file1_path)
            if file2_path:
                self.logger.info("File 2: %s", file2_path)
            
            # Get record counts
            df1_count = df1.count()
            df2_count = df2.count()
            self.logger.info("Loaded %d records from file 1", df1_count)
            self.logger.info("Loaded %d records from file 2", df2_count)
            
            # Parse join keys
            join_key1, join_key2 = join_keys
            
            # Perform joins and analysis
            validation_results = self._analyze_data(df1, df2, join_key1, join_key2)
            
            processing_time = time.time() - start_time
            
            return {
                'status': 'success',
                'results': validation_results,
                'processing_time': processing_time,
                'metadata': {
                    'file1_path': file1_path,
                    'file2_path': file2_path,
                    'join_keys': f"{join_key1} {join_key2}",
                    'validation_config': validation_config
                }
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Validation workflow failed: {str(e)}"
            self.logger.error(error_msg)
            
            return {
                'status': 'error',
                'error': error_msg,
                'processing_time': processing_time
            }
    
    def _analyze_data(self, df1: DataFrame, df2: DataFrame, join_key1: str, join_key2: str) -> Dict[str, Any]:
        """Perform comprehensive data analysis and validation."""
        try:
            # Add prefixes to distinguish columns
            df1_prefixed = df1.select([col(c).alias(f"df1_{c}") for c in df1.columns])
            df2_prefixed = df2.select([col(c).alias(f"df2_{c}") for c in df2.columns])
            
            # Perform joins
            matched_df = df1_prefixed.join(
                df2_prefixed, 
                df1_prefixed[f"df1_{join_key1}"] == df2_prefixed[f"df2_{join_key2}"], 
                "inner"
            )
            
            # Missing records analysis
            missing_in_df1 = df2_prefixed.join(
                df1_prefixed, 
                df2_prefixed[f"df2_{join_key2}"] == df1_prefixed[f"df1_{join_key1}"], 
                "left_anti"
            )
            
            missing_in_df2 = df1_prefixed.join(
                df2_prefixed, 
                df1_prefixed[f"df1_{join_key1}"] == df2_prefixed[f"df2_{join_key2}"], 
                "left_anti"
            )
            
            # Get counts
            matched_count = matched_df.count()
            missing_in_df1_count = missing_in_df1.count()
            missing_in_df2_count = missing_in_df2.count()
            total_df1 = df1.count()
            total_df2 = df2.count()
            
            # Calculate metrics
            match_percentage = (matched_count / min(total_df1, total_df2)) * 100 if min(total_df1, total_df2) > 0 else 0
            coverage_percentage = (matched_count / max(total_df1, total_df2)) * 100 if max(total_df1, total_df2) > 0 else 0
            
            return {
                'matched_df': matched_df,
                'missing_in_df1': missing_in_df1,
                'missing_in_df2': missing_in_df2,
                'summary': {
                    'total_records_df1': total_df1,
                    'total_records_df2': total_df2,
                    'matched_count': matched_count,
                    'missing_in_df1_count': missing_in_df1_count,
                    'missing_in_df2_count': missing_in_df2_count,
                    'match_percentage': match_percentage,
                    'coverage_percentage': coverage_percentage,
                    'total_records': total_df1 + total_df2,
                    'total_missing': missing_in_df1_count + missing_in_df2_count
                }
            }
            
        except Exception as e:
            self.logger.error(f"Data analysis failed: {str(e)}")
            raise