#!/usr/bin/env python3
"""
Optimized Forex Validation Script

A streamlined forex validation tool that consolidates all functionality
into a single, efficient class with minimal dependencies.
"""

import sys
import time
from pathlib import Path
from typing import Dict, Any, Tuple, List
from dataclasses import dataclass

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))
from src.utils.spark_utils import *
from src.utils.custom_logger import get_logger
from src.utils.s3_paths import S3Paths
from src.jobs.data_validation.service.validation_service import DataValidatorService


@dataclass
class ValidationConfig:
    """Simplified validation configuration."""
    include_data_quality: bool = True
    include_missing_analysis: bool = True
    include_field_comparison: bool = True
    target_fields: List[str] = None
    
    def __post_init__(self):
        if self.target_fields is None:
            self.target_fields = ["quantity", "unit_price"]


class ForexValidation:
    """
    Optimized Forex Validation class that consolidates all validation functionality
    into a single, efficient class with minimal external dependencies.
    """
    
    def __init__(self, config: dict, **kwargs):
        """
        Initialize Forex Validation with configuration parameters.
        
        Args:
            config: Configuration dictionary containing bucket_path, t_1, t_2
            **kwargs: Additional parameters for file paths and options
        """
        self.spark = None
        self.data_validator_service = None
        # Core configuration
        self.config = config
        self.bucket_path = config.get("bucket_path")
        self.t_1 = config["t_1"]
        self.t_2 = config.get("t_2", self.t_1)
        
        # File paths with defaults
        self.file1_path = kwargs.get("file1_path", f"{self.bucket_path}/{S3Paths.forex_transactions}/dt={self.t_1}")
        self.file2_path = kwargs.get("file2_path", f"{self.bucket_path}/{S3Paths.forex_unhedged_transactions}/dt={self.t_1}")
        self.output_path = kwargs.get("output_path", f"{self.bucket_path}/{S3Paths.data_validation_forex_unhedged_transaction}/dt={self.t_1}")
        self.download_path = kwargs.get("download_path", "hdfs:///home/<USER>/forex_validation_results")
        
        # Validation parameters
        self.join_keys = kwargs.get("join_keys", "id transaction_id")
        self.output_format = kwargs.get("output_format", "csv")
        self.estimate_time = kwargs.get("estimate_time", True)

        self.logger = get_logger(env=self.config.get("env"), tag="Forex Transaction Validation")
        
        # Spark setup
        self.app_name = "ForexValidation"
        self.spark_utils = SparkUtils(self.app_name)
        self.spark = self._setup_spark_session()

        self.io_utils = IOUtils(self.spark, self.config)
        
        # Validation configuration
        self.validation_config = ValidationConfig()
        
        # Create output directory if it doesn't exist
        self._ensure_output_directory()
    
    def _ensure_output_directory(self) -> None:
        """Ensure output directory is ready (HDFS will create it automatically when writing)."""
        try:
            # For HDFS paths, we don't need to create directories manually
            # HDFS will create them automatically when we write files
            self.logger.info("Output directory will be created automatically in HDFS: %s", self.download_path)
        except Exception as e:
            self.logger.error("Failed to prepare output directory %s: %s", self.download_path, str(e))
            raise
    
    # Core validation methods
    def _validate_config(self) -> None:
        """Validate configuration parameters."""
        if not all([self.file1_path, self.file2_path, self.download_path, self.join_keys]):
            raise ValueError("Missing required arguments: file1_path, file2_path, output_path, join_keys")
        
        if self.output_format not in ["csv", "parquet", "json"]:
            raise ValueError(f"Invalid output format: {self.output_format}")
    
    def _setup_spark_session(self) -> SparkSession:
        """Create and configure Spark session."""
        self.spark = self.spark_utils.create_spark_session()
        # Initialize data validator service after spark session is created
        self.data_validator_service = DataValidatorService(self.logger, self.config, self.spark)
        return self.spark
    
    def _stop_spark_session(self) -> None:
        """Stop Spark session."""
        if self.spark is not None:
            self.spark_utils.stop_spark(self.spark)
            self.spark = None
    
    def _load_data(self, file_path: str) -> DataFrame:
        """Load data from file path with automatic format detection for S3 folders."""
        try:
            # Check if it's an S3 path (folder)
            if file_path.startswith('s3a://') or file_path.startswith('s3://'):
                df = self._load_s3_data(file_path)
            # Handle local files
            elif file_path.endswith('.csv'):
                df = self.io_utils.read_csv_file(file_path)
            elif file_path.endswith('.parquet'):
                df = self.io_utils.read_parquet_data(file_path)
            elif file_path.endswith('.json'):
                df = self.io_utils.read_json_data(file_path)
            else:
                df = self.io_utils.read_csv_file(file_path)
            
            # Log the schema
            self.logger.info("Schema for %s:", file_path)
            self.logger.info("Columns: %s", df.columns)
            self.logger.info("Data types:")
            return df
        except Exception as e:
            self.logger.error("Failed to load data from %s: %s", file_path, str(e))
            raise
    
    def _load_s3_data(self, s3_path: str) -> DataFrame:
        """Load data from S3 path, automatically detecting format (Parquet or CSV)."""
        try:
            # First try to read as Parquet (most common format in data lakes)
            try:
                self.logger.info("Attempting to load as Parquet from: %s", s3_path)
                df = self.io_utils.read_parquet_data(s3_path)
                self.logger.info("Successfully loaded as Parquet format")
                return df
            except Exception as parquet_error:
                self.logger.info("Parquet read failed, trying CSV format: %s", str(parquet_error))
                
                # If Parquet fails, try CSV
                try:
                    self.logger.info("Attempting to load as CSV from: %s", s3_path)
                    df = self.io_utils.read_csv_file(s3_path)
                    self.logger.info("Successfully loaded as CSV format")
                    return df
                except Exception as csv_error:
                    self.logger.error("Both Parquet and CSV read failed for path: %s", s3_path)
                    self.logger.error("Parquet error: %s", str(parquet_error))
                    self.logger.error("CSV error: %s", str(csv_error))
                    raise Exception(f"Failed to read data from S3 path {s3_path}. Tried both Parquet and CSV formats.")
                    
        except Exception as e:
            self.logger.error("Failed to load S3 data from %s: %s", s3_path, str(e))
            raise
    
    
    def _perform_validation_with_service(self) -> Dict[str, Any]:
        """Perform validation using the data validator service."""
        try:
            self.logger.info("Starting validation workflow...")
            self.logger.info("File 1: %s", self.file1_path)
            self.logger.info("File 2: %s", self.file2_path)
            self.logger.info("Output: %s", self.download_path)
            self.logger.info("Join keys: %s", self.join_keys)
            
            # Load data
            self.logger.info("Loading input files...")
            df1 = self._load_data(self.file1_path).select("user_id", "account_id","centralized_wallet_id", "client_id", "created", "fee", "forex_id", "id", "partner_id", "partner_price_id", "quantity", "ref_id", "status", "total_price", 
            "transaction_fee_config_id", "transaction_time", "transaction_type", "unit_price","updated")
            df2 = self._load_data(self.file2_path).select("id", "transaction_id", "table_name", "forex_id", "quantity", "unit_price", "transaction_time","status", "inventory_order_id", "created", "updated")
            
            # Parse join keys
            join_key1, join_key2 = self.join_keys.split()
            join_keys_tuple = (join_key1, join_key2)
            
            # Convert validation config to dict
            validation_config_dict = {
                'include_data_quality': self.validation_config.include_data_quality,
                'include_missing_analysis': self.validation_config.include_missing_analysis,
                'include_field_comparison': self.validation_config.include_field_comparison,
                'target_fields': self.validation_config.target_fields
            }
            
            # Perform validation using the service
            validation_results = self.data_validator_service.perform_validation(
                df1=df1,
                df2=df2,
                join_keys=join_keys_tuple,
                validation_config=validation_config_dict,
                file1_path=self.file1_path,
                file2_path=self.file2_path
            )
            
            # Write results
            output_files = self._write_results(validation_results['results'])
            
            # Add output files to results
            validation_results['output_files'] = output_files
            
            return validation_results
            
        except Exception as e:
            error_msg = f"Validation workflow failed: {str(e)}"
            self.logger.error(error_msg)
            
            return {
                'status': 'error',
                'error': error_msg,
                'processing_time': 0
            }
    
    
    
    def _write_results(self, validation_results: Dict[str, Any]) -> Dict[str, str]:
        """Write validation results to output files."""
        try:
            output_files = {}
            
            # Create output directory (HDFS will create it automatically when writing)
            self.logger.info("Writing results to HDFS path: %s", self.download_path)
            
            # Write matched records
            matched_df = validation_results['matched_df']
            matched_path = f"{self.download_path}/matched_records"
            self._write_dataframe(matched_df, matched_path)
            output_files['matched_records'] = matched_path
            
            # Write missing records
            missing_in_df1 = validation_results['missing_in_df1']
            missing_in_df2 = validation_results['missing_in_df2']
            
            if missing_in_df1.count() > 0:
                missing_path1 = f"{self.download_path}/missing_in_forex_transactions"
                self._write_dataframe(missing_in_df1, missing_path1)
                output_files['missing_in_forex_transactions'] = missing_path1
            
            if missing_in_df2.count() > 0:
                missing_path2 = f"{self.download_path}/missing_in_forex_unhedged_transactions"
                self._write_dataframe(missing_in_df2, missing_path2)
                output_files['missing_in_forex_unhedged_transactions'] = missing_path2
            
            return output_files
            
        except Exception as e:
            self.logger.error(f"Failed to write results: {str(e)}")
            raise
    
    def _write_dataframe(self, df: DataFrame, output_path: str) -> None:
        """Write DataFrame to file with proper formatting (supports S3 and HDFS)."""
        try:
            # For S3/HDFS, we need to accept that Spark creates directories
            # The final path will be a directory containing the part files
            if self.output_format == "csv":
                # Write as CSV - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").option("header", "true").csv(output_path)
            elif self.output_format == "parquet":
                # Write as Parquet - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").parquet(output_path)
            elif self.output_format == "json":
                # Write as JSON - Spark will create a directory with part files
                df.coalesce(1).write.mode("overwrite").json(output_path)
            
            self.logger.info(f"Written results to directory: {output_path}")
            self.logger.info("Note: Spark creates a directory with part files - this is normal behavior")
            
        except Exception as e:
            self.logger.error(f"Failed to write DataFrame to {output_path}: {str(e)}")
            raise
    
    def _post_process_matched_records(self, matched_csv_path: str) -> None:
        """Post-process matched records to check for field mismatches."""
        self.logger.info("Starting post-processing of matched records...")
        
        try:
            # Read the matched records
            matched_df = self.io_utils.read_csv_file(matched_csv_path)
            self.logger.info(f"Available columns in matched records: {matched_df.columns}")
            
            # Find comparable field pairs
            comparable_fields = self._find_comparable_fields(matched_df)
            
            if not comparable_fields:
                self.logger.info("No comparable fields found between the two datasets")
                return
            
            # Analyze field mismatches
            self._analyze_field_mismatches(matched_df, comparable_fields, matched_csv_path)
                
        except Exception as e:
            self.logger.error(f"Error in post-processing matched records: {str(e)}")
            raise
    
    def _find_comparable_fields(self, matched_df: DataFrame) -> List[Tuple[str, str, str]]:
        """Find comparable field pairs between datasets."""
        comparable_fields = []
        target_fields = self.validation_config.target_fields
        
        for field_name in target_fields:
            df1_col = f"df1_{field_name}"
            df2_col = f"df2_{field_name}"
            if df1_col in matched_df.columns and df2_col in matched_df.columns:
                comparable_fields.append((df1_col, df2_col, field_name))
        
        self.logger.info(f"Found {len(comparable_fields)} comparable field pairs: {[f[2] for f in comparable_fields]}")
        return comparable_fields
    
    def _analyze_field_mismatches(self, matched_df: DataFrame, comparable_fields: List[Tuple[str, str, str]], matched_csv_path: str) -> None:
        """Analyze and report field mismatches."""
        # Create a comprehensive mismatch condition for any field
        overall_mismatch_condition = None
        field_mismatch_conditions = {}
        
        for df1_field, df2_field, field_name in comparable_fields:
            # Create condition for field mismatch
            mismatch_condition = (col(df1_field) != col(df2_field)) | \
                               (col(df1_field).isNull() & col(df2_field).isNotNull()) | \
                               (col(df1_field).isNotNull() & col(df2_field).isNull())
            
            field_mismatch_conditions[field_name] = mismatch_condition
            
            # Build overall condition (OR of all field conditions)
            if overall_mismatch_condition is None:
                overall_mismatch_condition = mismatch_condition
            else:
                overall_mismatch_condition = overall_mismatch_condition | mismatch_condition
        
        if overall_mismatch_condition is not None:
            # Get all records that have any field mismatch
            records_with_mismatches = matched_df.filter(overall_mismatch_condition)
            
            if records_with_mismatches.count() > 0:
                # Build select columns - include ID columns and all comparable field pairs
                select_columns = [
                    col("df1_id").alias("df1_id"),
                    col("df2_transaction_id").alias("df2_transaction_id")
                ]
                
                # Add all comparable field pairs to the output
                for df1_field, df2_field, field_name in comparable_fields:
                    select_columns.extend([
                        col(df1_field).alias(f"df1_{field_name}"),
                        col(df2_field).alias(f"df2_{field_name}")
                    ])
                
                # Select all relevant columns for mismatched records
                comprehensive_mismatches = records_with_mismatches.select(*select_columns)
                
                # Process and report mismatches
                self._process_comprehensive_field_mismatches(comprehensive_mismatches, field_mismatch_conditions, comparable_fields)
            else:
                self.logger.info("No field mismatches found in matched records")
        else:
            self.logger.info("No comparable fields found for mismatch analysis")
    
    def _process_comprehensive_field_mismatches(self, comprehensive_mismatches: DataFrame, field_mismatch_conditions: Dict[str, any], comparable_fields: List[Tuple[str, str, str]]) -> None:
        """Process comprehensive field mismatches with all relevant columns."""
        total_mismatch_count = comprehensive_mismatches.count()
        
        if total_mismatch_count > 0:
            self.logger.info(f"Found {total_mismatch_count} records with field mismatches.")
            
            # Analyze each field to report individual field mismatch counts
            field_mismatch_details = []
            for _, _, field_name in comparable_fields:
                # Count mismatches for this specific field
                field_condition = field_mismatch_conditions[field_name]
                field_mismatch_count = comprehensive_mismatches.filter(field_condition).count()
                
                if field_mismatch_count > 0:
                    self.logger.info(f"Found {field_mismatch_count} mismatches in field: {field_name}")
                    field_mismatch_details.append((field_name, field_mismatch_count))
                    
                    # Log sample mismatches for this field
                    self._log_comprehensive_sample_mismatches(comprehensive_mismatches, field_condition, field_name)
            
            # Write comprehensive mismatches to file
            self._write_comprehensive_mismatched_fields(comprehensive_mismatches, field_mismatch_details, total_mismatch_count)
        else:
            self.logger.info("No field mismatches found in matched records")
    
    def _process_field_mismatches(self, mismatched_records: List[Tuple[str, DataFrame]]) -> None:
        """Process and write field mismatches to output file."""
        total_mismatch_count = 0
        all_mismatch_details = []
        
        for field_name, mismatch_df in mismatched_records:
            field_mismatch_count = mismatch_df.count()
            
            if field_mismatch_count > 0:
                total_mismatch_count += field_mismatch_count
                self.logger.info(f"Found {field_mismatch_count} mismatches in field: {field_name}")
                
                # Log sample mismatches
                self._log_sample_mismatches(mismatch_df, field_name)
                all_mismatch_details.append((field_name, mismatch_df, field_mismatch_count))
        
        if total_mismatch_count > 0:
            self.logger.info(f"Found {total_mismatch_count} total field mismatches.")
            self._write_mismatched_fields(all_mismatch_details)
        else:
            self.logger.info("No field mismatches found in matched records")
    
    def _log_comprehensive_sample_mismatches(self, comprehensive_mismatches: DataFrame, field_condition: any, field_name: str) -> None:
        """Log sample mismatches for a specific field from comprehensive mismatch data."""
        field_mismatches = comprehensive_mismatches.filter(field_condition)
        sample_mismatches = field_mismatches.limit(5).collect()
        for j, row in enumerate(sample_mismatches, 1):
            row_dict = row.asDict()
            df1_id = row_dict.get('df1_id', 'N/A')
            df2_transaction_id = row_dict.get('df2_transaction_id', 'N/A')
            df1_value = row_dict.get(f'df1_{field_name}', 'N/A')
            df2_value = row_dict.get(f'df2_{field_name}', 'N/A')
            self.logger.info(f"  {j}. DF1 ID: {df1_id}, DF2 Transaction ID: {df2_transaction_id}, {field_name} - DF1: {df1_value}, DF2: {df2_value}")

    def _log_sample_mismatches(self, mismatch_df: DataFrame, field_name: str) -> None:
        """Log sample mismatches for a field."""
        sample_mismatches = mismatch_df.limit(5).collect()
        for j, row in enumerate(sample_mismatches, 1):
            row_dict = row.asDict()
            df1_id = row_dict.get('df1_id', 'N/A')
            df2_transaction_id = row_dict.get('df2_transaction_id', 'N/A')
            df1_value = row_dict.get(f'df1_{field_name}', 'N/A')
            df2_value = row_dict.get(f'df2_{field_name}', 'N/A')
            self.logger.info(f"  {j}. DF1 ID: {df1_id}, DF2 Transaction ID: {df2_transaction_id}, {field_name} - DF1: {df1_value}, DF2: {df2_value}")
    
    def _write_comprehensive_mismatched_fields(self, comprehensive_mismatches: DataFrame, field_mismatch_details: List[Tuple[str, int]], total_mismatch_count: int) -> None:
        """Write comprehensive field mismatches to output file."""
        # Use the S3 output path for writing unmatched fields
        unmatched_fields_path = f"{self.output_path}/unmatched_fields"
        self.logger.info("Writing comprehensive field mismatches to: %s", unmatched_fields_path)
        
        # Write comprehensive mismatches to file (includes all df1_* and df2_* columns)
        self._write_dataframe(comprehensive_mismatches, unmatched_fields_path)
        self.logger.info("Written comprehensive field mismatches to: %s", unmatched_fields_path)
        
        # Send Slack notification about field mismatches
        self._send_comprehensive_field_mismatches_slack_notification(field_mismatch_details, total_mismatch_count, unmatched_fields_path)

    def _write_mismatched_fields(self, all_mismatch_details: List[Tuple[str, DataFrame, int]]) -> None:
        """Write all mismatched fields to a single output file."""
        # Use the S3 output path for writing unmatched fields
        unmatched_fields_path = f"{self.output_path}/unmatched_fields"
        self.logger.info("Writing field mismatches to: %s", unmatched_fields_path)
        
        # Union all mismatch DataFrames
        combined_mismatches = all_mismatch_details[0][1]
        for _, mismatch_df, _ in all_mismatch_details[1:]:
            combined_mismatches = combined_mismatches.union(mismatch_df)
        
        # Write to file
        self._write_dataframe(combined_mismatches, unmatched_fields_path)
        self.logger.info("Written field mismatches to: %s", unmatched_fields_path)
        
        # Send Slack notification about field mismatches
        self._send_field_mismatches_slack_notification(all_mismatch_details, unmatched_fields_path)
    
    def _send_comprehensive_field_mismatches_slack_notification(self, field_mismatch_details: List[Tuple[str, int]], total_mismatch_count: int, unmatched_fields_path: str) -> None:
        """Send Slack notification about comprehensive field mismatches."""
        try:
            # Create field breakdown
            field_breakdown = []
            for field_name, count in field_mismatch_details:
                if count > 0:
                    field_breakdown.append(f"{field_name}: {count:,} mismatches")
            
            # Create Slack message
            field_breakdown_str = " | ".join(field_breakdown) if field_breakdown else "No field mismatches found"
            slack_message = f"Forex Validation Alert - Field Mismatches Detected | Total Records with Mismatches: {total_mismatch_count:,} | Fields with Issues: {len(field_mismatch_details)} | Breakdown: {field_breakdown_str} | S3 Location: {unmatched_fields_path} | Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}"
            
            # Log the Slack message (in production, you would send this to actual Slack)
            self.logger.warning("SLACK NOTIFICATION - Comprehensive Field Mismatches:")
            self.logger.warning(slack_message)            
        except Exception as e:
            self.logger.error(f"Failed to send Slack notification: {str(e)}")

    def _send_field_mismatches_slack_notification(self, all_mismatch_details: List[Tuple[str, DataFrame, int]], unmatched_fields_path: str) -> None:
        """Send Slack notification about field mismatches."""
        try:
            # Calculate total mismatches
            total_mismatches = sum(count for _, _, count in all_mismatch_details)
            
            # Create field breakdown
            field_breakdown = []
            for field_name, _, count in all_mismatch_details:
                if count > 0:
                    field_breakdown.append(f"{field_name}: {count:,} mismatches")
            
            # Create Slack message
            field_breakdown_str = " | ".join(field_breakdown) if field_breakdown else "No field mismatches found"
            slack_message = f"Forex Validation Alert - Field Mismatches Detected | Total Mismatches: {total_mismatches:,} | Fields with Issues: {len([field for _, _, field in all_mismatch_details if field > 0])} | Breakdown: {field_breakdown_str} | S3 Location: {unmatched_fields_path} | Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}"
            
            # Log the Slack message (in production, you would send this to actual Slack)
            self.logger.warning("SLACK NOTIFICATION - Field Mismatches:")
            self.logger.warning(slack_message)            
        except Exception as e:
            self.logger.error(f"Failed to send Slack notification: {str(e)}")
    
    def _send_missing_transactions_slack_notification(self, missing_df: DataFrame, s3_file_path: str, total_missing: int) -> None:
        """Send Slack notification about missing transactions."""
        try:
            # Get top 10 missing transaction IDs
            top_missing = missing_df.select("df2_transaction_id").limit(10).collect()
            
            # Create transaction ID list
            transaction_list = []
            for i, row in enumerate(top_missing, 1):
                transaction_id = row.df2_transaction_id
                transaction_list.append(str(transaction_id))
            
            # Create Slack message
            slack_message = f"Forex Validation Alert - Missing Transactions Detected | Total Missing: {total_missing:,} | S3 Location: {s3_file_path} | Top Missing IDs: {', '.join(transaction_list[:5]) if transaction_list else 'None'} | Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}"
            
            # Log the Slack message (in production, you would send this to actual Slack)
            self.logger.warning("SLACK NOTIFICATION - Missing Transactions:")
            self.logger.warning(slack_message)
            
        except Exception as e:
            self.logger.error(f"Failed to send missing transactions Slack notification: {str(e)}")
    
    def _send_unhedged_transactions_slack_notification(self, unhedged_df: DataFrame, s3_file_path: str, total_unhedged: int) -> None:
        """Send Slack notification about unhedged transactions."""
        try:
            # Get top 10 unhedged transactions
            top_unhedged = unhedged_df.select("df2_id", "df2_transaction_id").orderBy("df2_id").limit(10).collect()
            
            # Create transaction list
            transaction_list = []
            for i, row in enumerate(top_unhedged, 1):
                transaction_id = row.df2_transaction_id
                id_value = row.df2_id
                transaction_list.append(f"ID:{id_value},TX:{transaction_id}")
            
            # Create Slack message
            transaction_ids_str = ", ".join(transaction_list[:5]) if transaction_list else "None"
            slack_message = f"Forex Validation Alert - Unhedged Transactions Detected | Total Unhedged: {total_unhedged:,} | Top IDs: {transaction_ids_str} | S3 Location: {s3_file_path} | Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S UTC', time.gmtime())}"
            
            # Log the Slack message (in production, you would send this to actual Slack)
            self.logger.warning("SLACK NOTIFICATION - Unhedged Transactions:")
            self.logger.warning(slack_message)
            
        except Exception as e:
            self.logger.error(f"Failed to send unhedged transactions Slack notification: {str(e)}")
    
    def _run_post_processing(self, results: Dict[str, Any]) -> None:
        """Run post-processing on validation results."""
        self.logger.info("Starting post-processing of validation results...")
        
        try:
            output_files = results.get('output_files', {})
            matched_records_path = output_files.get('matched_records')
            
            if matched_records_path:
                self._post_process_matched_records(matched_records_path)
            else:
                self.logger.info("No matched records file found for post-processing")
            
            # Get output directory from the matched records path (HDFS compatible)
            if matched_records_path and "/" in matched_records_path:
                output_dir = "/".join(matched_records_path.split("/")[:-1])
            else:
                output_dir = matched_records_path
            
            # Additional post-processing
            if output_dir:
                self._process_missing_transactions(output_dir)
                self._process_unhedged_transactions(output_dir)
                
        except Exception as e:
            self.logger.error(f"Error in post-processing: {str(e)}")
            raise

    def _process_missing_transactions(self, output_dir: str) -> None:
        """Process missing transactions and log analysis."""
        try:
            missing_file_path = f"{output_dir}/missing_in_forex_transactions"
            s3_missing_file_path = f"{self.output_path}/missing_in_forex_transactions"
            
            # For HDFS, we'll try to read the file and catch exceptions if it doesn't exist
            try:
                missing_df = self.io_utils.read_csv_file(missing_file_path)
                # Force evaluation to check if file exists
                missing_df.limit(1).collect()
            except Exception:
                self.logger.info(f"Missing transactions file not found: {missing_file_path}")
                return
            total_missing = missing_df.count()
            
            if total_missing == 0:
                self.logger.info("No missing transactions found in forex_transactions.csv")
                return

            # Write missing transactions to S3
            self.logger.info("Writing missing transactions to S3: %s", s3_missing_file_path)
            self._write_dataframe(missing_df, s3_missing_file_path)
            
            # Send Slack notification about missing transactions
            self._send_missing_transactions_slack_notification(missing_df, s3_missing_file_path, total_missing)
            
        except Exception as e:
            self.logger.error(f"Error processing missing transactions file: {str(e)}")
            raise


    def _process_unhedged_transactions(self, output_dir: str) -> None:
        """Process unhedged transactions and create analysis file."""
        try:
            missing_file_path = f"{output_dir}/missing_in_forex_transactions"
            
            # For HDFS, we'll try to read the file and catch exceptions if it doesn't exist
            try:
                missing_df = self.io_utils.read_csv_file(missing_file_path)
                # Force evaluation to check if file exists
                missing_df.limit(1).collect()
            except Exception:
                self.logger.info(f"Missing transactions file not found: {missing_file_path}")
                return
            
            # Filter for unhedged transactions
            unhedged_df = missing_df.filter(col("df2_status") != "HEDGED")
            total_unhedged = unhedged_df.count()
            
            if total_unhedged == 0:
                self.logger.info("No unhedged transactions found - skipping unhedged_transactions.csv creation")
                return
            
            # Write unhedged transactions file to S3
            s3_unhedged_file_path = f"{self.output_path}/unhedged_transactions"
            self.logger.info("Writing unhedged transactions to S3: %s", s3_unhedged_file_path)
            self._write_dataframe(unhedged_df, s3_unhedged_file_path)
            
            
            # Send Slack notification about unhedged transactions
            self._send_unhedged_transactions_slack_notification(unhedged_df, s3_unhedged_file_path, total_unhedged)
            
        except Exception as e:
            self.logger.error(f"Error processing unhedged transactions file: {str(e)}")
            raise


    # Results and Summary Methods
    def _display_validation_summary(self, results: Dict[str, Any]) -> None:
        """Display validation summary."""
        if results['status'] != 'success':
            return
            
        summary = results.get('results', {}).get('summary', {})
        self.logger.info("Validation Summary:")
        self.logger.info(f"  Total records: {summary.get('total_records', 0)}")
        self.logger.info(f"  Matched records: {summary.get('matched_count', 0)}")
        self.logger.info(f"  Missing records: {summary.get('total_missing', 0)}")
        self.logger.info(f"  Match percentage: {summary.get('match_percentage', 0):.2f}%")
        self.logger.info(f"  Coverage percentage: {summary.get('coverage_percentage', 0):.2f}%")
        self.logger.info(f"  Processing time: {results.get('processing_time', 0):.2f} seconds")
    
    def _display_validation_insights(self, results: Dict[str, Any]) -> None:
        """Display validation insights."""
        if results['status'] != 'success':
            return
            
        summary = results.get('results', {}).get('summary', {})
        insights = self._generate_insights(summary)
        
        if insights:
            self.logger.info("Insights:")
            for insight in insights:
                self.logger.info(f"  - {insight}")
    
    def _generate_insights(self, summary: Dict[str, Any]) -> List[str]:
        """Generate insights from validation summary."""
        insights = []
        
        total_df1 = summary.get('total_records_df1', 0)
        total_df2 = summary.get('total_records_df2', 0)
        matched = summary.get('matched_count', 0)
        
        if total_df1 > 0 and total_df2 > 0:
            match_rate_df1 = (matched / total_df1) * 100
            match_rate_df2 = (matched / total_df2) * 100
            
            if match_rate_df1 < 50 or match_rate_df2 < 50:
                insights.append("Low match rate detected - consider reviewing join keys or data quality")
            elif match_rate_df1 < 80 or match_rate_df2 < 80:
                insights.append("Moderate match rate - some data discrepancies found")
            else:
                insights.append("High match rate - data appears consistent between sources")
        
        missing_df1 = summary.get('missing_in_df1_count', 0)
        missing_df2 = summary.get('missing_in_df2_count', 0)
        
        if missing_df1 > 0:
            insights.append(f"File 2 has {missing_df1} records not present in File 1")
        if missing_df2 > 0:
            insights.append(f"File 1 has {missing_df2} records not present in File 2")
        
        return insights
    
    def _display_output_files(self, results: Dict[str, Any]) -> None:
        """Display output files created."""
        output_files = results.get('output_files', {})
        self.logger.info(f"Output files created ({len(output_files)}):")
        for result_type, file_path in output_files.items():
            self.logger.info(f"  {result_type}: {file_path}")
    
    def _display_results(self, results: Dict[str, Any]) -> None:
        """Display complete validation results."""
        if results['status'] == 'success':
            self.logger.info("Validation completed successfully!")
            self._display_validation_summary(results)
            self._display_validation_insights(results)
            self._display_output_files(results)
        else:
            self.logger.error(f"Validation failed: {results.get('error', 'Unknown error')}")
            raise RuntimeError(f"Validation failed: {results.get('error', 'Unknown error')}")
    
    # Main Orchestration Method
    def run(self) -> None:
        """Run the complete validation workflow."""
        try:
            # Validate configuration
            self._validate_config()
            
            
            # Estimate processing time if requested
            if self.estimate_time:
                time_estimate = self.data_validator_service.estimate_processing_time(self.file1_path, self.file2_path)
                self.logger.info(f"Estimated processing time: {time_estimate['estimated_time_minutes']:.2f} minutes")
                self.logger.info(f"Total file size: {time_estimate['total_size_mb']:.2f} MB")
                if time_estimate['recommendations']:
                    self.logger.info("Recommendations:")
                    for rec in time_estimate['recommendations']:
                        self.logger.info(f"  - {rec}")
            
            # Run validation
            results = self._perform_validation_with_service()
            
            # Post-processing
            self._run_post_processing(results)
            
            # Display results
            self._display_results(results)
            
        except Exception as e:
            self.logger.error(f"Validation failed with error: {str(e)}")
            raise
        finally:
            self._stop_spark_session()
    