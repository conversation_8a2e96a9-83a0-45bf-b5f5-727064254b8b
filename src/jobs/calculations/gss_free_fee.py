from src.utils.spark_utils import *
from datetime import datetime, time, timezone


class GSSFreeFee:
    def __init__(self, config: dict, **kwargs):
        self.logger = get_logger()
        self.config = config

        # utils
        self.spark_utils = SparkUtils("gss_free_fees")
        self.spark = self.spark_utils.create_spark_session()
        self.ops = Operations(self.spark)
        self.io_utils = IOUtils(self.spark, self.config)

        # paths and params from config
        self.bucket_path = self.config.get("bucket_path")
        self.offset = self.config["offset"]
        self.pluang_partner_id = self.config["pluang_partner_id"]
        self.consider_transaction_date = self.config["gss_free_fees"]["capping_free_fees_start"]
        self.free_fee_capping = self.config["gss_fee_waiver"]["gss_stock_free_fee_capping_for_new_user"]
        self.write_free_fee_path = self.config["gss_free_fees"]["write_free_fee"]

        self.t_1 = self.config["t_1"]
        self.logger.info(f"gss_free_fees execution_date (JKT): {self.t_1}")

    def load_gss_transaction_base(self):
        """Load base GSS transaction data with status and partner filters."""
        path = f"{self.bucket_path}/{S3Paths.global_stock_transactions}/dt={self.t_1}/*"
        gss_transaction = self.io_utils.read_parquet_data(path)
        gss_transaction = gss_transaction.filter(
            (col("status").isin(Constants.SUCCESS, Constants.PARTIALLY_FILLED, Constants.PENDING)) &
            (col("partner_id") == self.pluang_partner_id)
        )
        gss_transaction = gss_transaction.select(
            "account_id", "user_id", "waived_off_fee", "created", "transaction_time"
        )
        return gss_transaction

    def get_first_txn_users_last_30_days(self, base_transactions):
        """Get users whose first transaction was in the last 30 days."""
        win = Window.partitionBy("account_id").orderBy(F.to_timestamp("transaction_time"))
        first_txn = (
            base_transactions
            .withColumn("rn", F.row_number().over(win))
            .filter(col("rn") == 1)
        )
        
        days_since = F.datediff(F.to_date(lit(self.t_1)), F.to_date(col("transaction_time")))
        first_txn_users = (
            first_txn
            .filter((days_since >= 0) & (days_since < 30))
            .select("user_id", "account_id", col("transaction_time").alias("first_txn_time"))
        )
        
        return first_txn_users

    def find_gss_transaction(self, base_transactions):
        """Get GSS transactions filtered by created date window."""
        dt_utc = DateUtils.get_jkt_timestamp(self.offset).replace(hour=8, minute=0, second=0, microsecond=0).strftime("%Y-%m-%dT%H:%M:%S.000Z")
        gss_transaction = base_transactions.filter(
            (col("created") >= self.consider_transaction_date) & (col("created") <= dt_utc)
        ).drop("created")        
        gss_transaction = gss_transaction.withColumnRenamed("user_id", "user_id_dup")
        
        return gss_transaction

    def find_eligible_user(self, first_txn_users, gss_transaction):
        """Join first-time users with transactions and aggregate fees by account."""
        first_txn_alias = first_txn_users.alias("ftxn")
        gss_txn_alias = gss_transaction.alias("gtxn")
        
        eligible_user_today = first_txn_alias.join(
            gss_txn_alias,
            col("ftxn.user_id") == col("gtxn.user_id_dup"),
            "left"
        ).select(
            col("ftxn.user_id").alias("user_id"),
            col("ftxn.account_id").alias("account_id"),
            col("gtxn.waived_off_fee").alias("waived_off_fee"),
            col("gtxn.transaction_time").alias("transaction_time")
        ).fillna(0.0)

        user_with_fees = eligible_user_today.groupBy(
            "user_id", "account_id"
        ).agg(F.round(F.sum("waived_off_fee"), 2).alias("total_gss_fee_waived")).filter(
            col("total_gss_fee_waived") > 0)
        
        return user_with_fees

    def build_mongo_config(self) -> dict:
        mongo_config = self.config["data_store"]["reporting_mongo"]
        mongo_collection = self.config["data_store"]["user_price_tiering"]["collection"]
        mongo_config['collection'] = mongo_collection
        mongo_uri = self.io_utils.get_mongo_connection_string(mongo_config)
        return {
            "uri": mongo_uri,
            "collection": mongo_collection,
            "batch_size": "500",
            "mode": "append"
        }

    def start_processing(self):
        self.logger.info("Loading base gss transaction data")
        base_transactions = self.load_gss_transaction_base()

        self.logger.info("Identifying users with first transaction in last 30 days")
        first_txn_users = self.get_first_txn_users_last_30_days(base_transactions)

        self.logger.info("Filtering gss transactions by date window")
        gss_transaction = self.find_gss_transaction(base_transactions)

        self.logger.info("Computing eligible users and fees")
        user_with_fees = self.find_eligible_user(first_txn_users, gss_transaction)
        user_with_fees = user_with_fees.filter(col("account_id") != 0)

        self.logger.info("Writing CSV snapshot")
        output_path = f"{self.bucket_path}/{self.write_free_fee_path}/dt={self.t_1}/"
        self.io_utils.write_csv_file(user_with_fees, output_path)

        self.logger.info("Preparing dataset for Mongo write")
        user_with_fees = user_with_fees.withColumn(
            "gss_fee_max_cap_reached",
            when(col("total_gss_fee_waived") > self.free_fee_capping, lit(True)).otherwise(False)
        )
        user_with_fees = user_with_fees.select(
            "user_id", "account_id", "total_gss_fee_waived", "gss_fee_max_cap_reached"
        )
        user_with_fees = user_with_fees.withColumn("updated_at", lit(DateUtils.get_utc_timestamp()))

        self.io_utils.write_dataset_to_mongo(
            df=user_with_fees,
            mongo_config=self.build_mongo_config(),
            asset_name="gss_free_fees",
            write_format="update",
            shardkey="{'accountId':1}",
            add_created_at=False
        )

    def run(self):
        self.start_processing()
        self.spark_utils.stop_spark(self.spark)


