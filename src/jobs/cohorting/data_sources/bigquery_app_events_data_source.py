"""
BigQuery App Events Data Source.

Handles reading app events from BigQuery for t_1 date with aggregation and asset_type normalization.
"""

from typing import List, Dict, Any
from pyspark.sql import DataFrame
from src.jobs.cohorting.data_sources.base_data_source import BaseDataSource
from datetime import date, timedelta, datetime, timezone


class BigQueryAppEventsDataSource(BaseDataSource):
    """
    Data source for App Events data from BigQuery.

    Reads from BigQuery daily sharded tables (events_YYYYMMDD) for t_1 date.
    Normalizes asset_type (Crypto → crypto_currency) and aggregates view counts.
    Filter on view_count is applied at cohort level.
    """

    name = "BigQueryAppEventsDataSource"

    def __init__(self, spark, config, logger):
        super().__init__(spark, config, logger)
        self.bq_dataset = self.config.get("bq_app_events_db")
        self.table_prefix = "events_intraday_"
        self.event_name = "adp_landing"
        self.t_1 = self.t_1

    def read(self, params: Dict[str, Any], required_columns: List[str]) -> DataFrame:
        """
        Read App Events data from BigQuery for t_1 date.

        Args:
            params: Parameters (not used; reads t_1 from config)
            required_columns: Required columns to enforce

        Returns:
            DataFrame with aggregated view counts per user+asset
        """
        table_date = str(self.t_1).replace("-", "")

        # Try primary table prefix, fallback to events_ if empty
        last_error = None
        for prefix in [self.table_prefix, "events_"]:
            full_table = f"{self.bq_dataset}.{prefix}{table_date}"
            self.logger.info(f"Reading BigQuery table: {full_table} for t_1={self.t_1}")

            query = self._build_aggregation_query(full_table)
            try:
                df = self.io_utils.read_from_bigquery(database_name=self.bq_dataset, query=query)
                if df is not None and df.count() > 0:
                    df_final = self._enforce_required_columns(df, required_columns)
                    self.logger.info(f"Fetched {df_final.count()} rows from {full_table}")
                    return df_final
                else:
                    self.logger.warning(f"No data found in {full_table}, trying fallback table")
            except Exception as e:
                last_error = e
                self.logger.warning(f"Failed to read {full_table}: {e}. Trying fallback table...")
                continue

        # If we exhausted all fallback options and still have an error, log and return empty
        if last_error:
            self.logger.error(f"All table attempts failed. Last error: {last_error}")

        return self._create_empty_dataframe(required_columns)

    def _build_aggregation_query(self, full_table: str) -> str:
        """
        Build BigQuery SQL query for aggregated views with normalization.

        Args:
            full_table: Full table reference (dataset.table_name)

        Returns:
            SQL query string
        """
        whitelist_db = self.config.get("bq_de_reporting_db")
        whitelist_table = f"{whitelist_db}.cohorting_whitelisted_page_views_assets"

        query = f"""
        WITH base AS (
          SELECT
            user_id,
            TIMESTAMP_MICROS(event_timestamp) AS event_ts,
            MAX(IF(ep.key = 'asset_type',   ep.value.string_value, NULL)) AS asset_type_raw,
            MAX(IF(ep.key = 'asset_symbol', ep.value.string_value, NULL)) AS asset_symbol
          FROM `{full_table}`
          CROSS JOIN UNNEST(event_params) AS ep
          WHERE event_name = '{self.event_name}'
            AND DATE(TIMESTAMP_MICROS(event_timestamp)) = DATE('{self.t_1}')
          GROUP BY user_id, event_timestamp
        ),
        filtered_base AS (
          SELECT
            b.user_id,
            b.asset_type_raw,
            b.asset_symbol
          FROM base b
          WHERE b.asset_symbol IS NOT NULL
            AND b.asset_type_raw IN ('USSS', 'Crypto', 'Crypto Futures')
            AND EXISTS (
              SELECT 1
              FROM `{whitelist_table}` w
              WHERE w.asset_symbol = b.asset_symbol
                AND w.asset_type = CASE
                  WHEN b.asset_type_raw = 'USSS' THEN 'USSS'
                  WHEN b.asset_type_raw IN ('Crypto','Crypto Futures') THEN 'CRYPTO'
                  ELSE 'OTHER'
                END
            )
        )
        SELECT
          user_id,
          CASE 
            WHEN asset_type_raw = 'USSS' THEN 'global_stocks'
            WHEN asset_type_raw = 'Crypto' THEN 'crypto_currency'
            WHEN asset_type_raw = 'Crypto Futures' THEN 'crypto_future'
            ELSE asset_type_raw
          END AS asset_type,
          asset_symbol,
          COUNT(*) AS view_count
        FROM filtered_base
        GROUP BY user_id, asset_type, asset_symbol
        """
        return query

    def _enforce_required_columns(self, df: DataFrame, required_columns: List[str]) -> DataFrame:
        """Enforce required columns by selecting available and adding missing as nulls."""
        if not required_columns:
            return df

        # Select available columns
        available = [c for c in required_columns if c in df.columns]
        if available:
            df = df.select(*available)

        # Add missing columns as null
        for col_name in required_columns:
            if col_name not in df.columns:
                from pyspark.sql.functions import lit
                df = df.withColumn(col_name, lit(None))

        return df

