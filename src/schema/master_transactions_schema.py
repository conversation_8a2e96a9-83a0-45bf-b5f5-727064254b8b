"""
Master Transaction Schemas
Defines PySpark schemas for all master transaction asset types based on test data structure.
"""

from pyspark.sql.types import StructType, StructField, StringType, IntegerType, DoubleType, BooleanType, TimestampType, LongType, DecimalType
from src.schema import crypto_currency_schema, global_stocks_schema, crypto_futures_schema, fund_schema, forex_schema, gold_schema, options_schema, cash_transactions_schema, indo_stocks_v2_schema


def get_crypto_currency_transactions_schema():
    """Schema for crypto_currency_transactions based on test data structure"""
    return crypto_currency_schema.crypto_currency_transactions_schema


def get_crypto_currency_pocket_transactions_schema():
    """Schema for crypto_currency_pocket_transactions"""
    return crypto_currency_schema.crypto_currency_pocket_transactions_schema


def get_crypto_currency_mission_rewards_schema():
    """Schema for crypto_currency_mission_rewards"""
    return crypto_currency_schema.crypto_currency_mission_rewards_schema


def get_crypto_currency_pluangcuan_yields_schema():
    """Schema for crypto_currency_pluangcuan_yields aligned to raw fields."""
    return StructType([
        StructField("id", StringType(), True),
        StructField("crypto_currency_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("yield_quantity", DoubleType(), True),
        StructField("unit_price", DoubleType(), True),
        StructField("yield_percentage", DoubleType(), True),
        StructField("status", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("effective_date", StringType(), True),
        StructField("info", StringType(), True),
        StructField("principal_quantity", DoubleType(), True),
    ])


def get_crypto_margin_wallet_transfers_schema():
    """Schema for crypto_margin_wallet_transfers"""
    return crypto_futures_schema.crypto_margin_wallet_transfers_schema


def get_global_stock_transactions_schema():
    """Schema for global_stock_transactions"""
    return StructType([
        StructField("id", StringType(), True),
        StructField("global_stock_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("fee", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("info", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("exchange_total_price", StringType(), True),
        StructField("source", StringType(), True),
        StructField("executed_quantity", StringType(), True),
        StructField("executed_unit_price", StringType(), True),
        StructField("executed_total_price", StringType(), True),
        StructField("order_type", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("taxes_and_fees", StringType(), True),
        StructField("taxation_fee", StringType(), True),
        StructField("fx_rate", StringType(), True),
        StructField("fx_currency", StringType(), True),
    ])


def get_schema_for_source(source_name: str):
    """Get schema for a specific data source"""
    schema_mapping = {
        "crypto_currency_transactions": get_crypto_currency_transactions_schema,
        "crypto_currency_pocket_transactions": get_crypto_currency_pocket_transactions_schema,
        "crypto_currency_mission_rewards": get_crypto_currency_mission_rewards_schema,
        "crypto_currency_pluangcuan_yields": get_crypto_currency_pluangcuan_yields_schema,
        "crypto_margin_wallet_transfers": get_crypto_margin_wallet_transfers_schema,
        "global_stock_transactions": get_global_stock_transactions_schema,
        # Global stocks lookups and auxiliary sources (minimal schemas)
        "global_stocks": lambda: StructType([
            StructField("id", StringType(), True),
            StructField("company_code", StringType(), True),
            StructField("stock_type", StringType(), True),
        ]),
        "global_stock_mission_rewards": lambda: StructType([
            StructField("id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("account_id", StringType(), True),
            StructField("global_stock_id", StringType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("transaction_time", TimestampType(), True),
        ]),
        "global_stock_split_transactions": lambda: StructType([
            StructField("id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("account_id", StringType(), True),
            StructField("global_stock_id", StringType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("distribution_date", TimestampType(), True),
            StructField("created", TimestampType(), True),
        ]),
        "global_stock_reverse_stock_split_transactions": lambda: StructType([
            StructField("id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("account_id", StringType(), True),
            StructField("global_stock_id", StringType(), True),
            StructField("quantity", DoubleType(), True),
            StructField("transaction_date", TimestampType(), True),
            StructField("created", TimestampType(), True),
        ]),
        "global_stock_inventory_orders": lambda: StructType([
            StructField("id", StringType(), True),
            StructField("user_id", StringType(), True),
            StructField("account_id", StringType(), True),
            StructField("created", TimestampType(), True),
        ]),
        "gold_transactions": lambda: gold_schema.gold_transactions_schema,
        # Newly added sources below
        "options_contract_transactions": lambda: global_stocks_schema.options_contract_transactions_schema,
        "gold_withdrawals": lambda: gold_schema.gold_withdrawals_schema,
        "fund_transactions": lambda: fund_schema.fund_transactions_schema,
        # Forex
        "forex_transactions": lambda: forex_schema.forex_transactions_schema,
        "forex_top_ups": lambda: forex_schema.forex_top_ups_schema,
        "forex_cash_outs": lambda: forex_schema.forex_cash_outs_schema,
        # Crypto futures
        "crypto_future_transactions": lambda: crypto_futures_schema.crypto_future_transactions_schema,
        "crypto_future_funding_transactions": lambda: get_crypto_future_funding_transactions_schema(),
    }
    
    if source_name in schema_mapping:
        return schema_mapping[source_name]()
    else:
        print(f"⚠️  No schema defined for source: {source_name}")
        return None


# Additional schema constructors for sources not covered in submodules
def get_crypto_future_funding_transactions_schema():
    """Schema for crypto_future_funding_transactions based on available raw fields; missing fields will be set to NULL downstream."""
    return crypto_futures_schema.crypto_future_funding_transactions_schema
