from src.utils.spark_utils import *

crypto_currency_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("crypto_currency_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("quantity", DecimalType(38, 18), False),
    StructField("unit_price", DecimalType(38, 18), False),
    StructField("fee", DecimalType(38, 18), False),
    StructField("total_price", DecimalType(38, 18), False),
    StructField("ref_id", StringType(), False),
    StructField("status", StringType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("info", StringType(), True),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("exchange_total_price", DecimalType(38, 18), True),
    StructField("source", StringType(), False),
    StructField("hedging_status", StringType(), False),
    StructField("unhedged_quantity", DecimalType(38, 18), True),
    StructField("hedging_info", StringType(), True),
    StructField("yield_quantity_used", DecimalType(38, 18), False),
    StructField("premium_fee", DecimalType(38, 18), False),
    StructField("transaction_fee", DecimalType(38, 18), False),
    StructField("estimated_quantity", DecimalType(38, 18), False),
    StructField("estimated_unit_price", DecimalType(38, 18), False),
    StructField("executed_quantity", DecimalType(38, 18), True),
    StructField("executed_unit_price", DecimalType(38, 18), True),
    StructField("executed_total_price", DecimalType(38, 18), True),
    StructField("vendor_transaction_id", StringType(), True),
    StructField("order_type", StringType(), False),
    StructField("price_info", StringType(), True),
    StructField("reward_quantity_used", DecimalType(38, 18), True),
    StructField("taxation_fee", DecimalType(38, 18), True),
    StructField("is_tax_reported", BooleanType(), True),
    StructField("lock_type", StringType(), True),
    StructField("lock_info", StringType(), True),
    StructField("matching_info", StringType(), True),
    StructField("recurring_order_type", StringType(), True),
    StructField("recurring_transaction_id", StringType(), True),
    StructField("system_status", StringType(), True),
    StructField("advanced_order_info", StringType(), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("parent_transaction_id", IntegerType(), True),
    StructField("taxes_and_fees", StringType(), True),
])

crypto_currency_pocket_transactions_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("crypto_currency_id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("user_pocket_id", IntegerType(), False),
    StructField("quantity", DecimalType(38, 18), False),
    StructField("unit_price", DecimalType(38, 18), False),
    StructField("transaction_fee", DecimalType(38, 18), False),
    StructField("taxation_fee", DecimalType(38, 18), True),
    StructField("total_price", DecimalType(38, 18), False),
    StructField("estimated_quantity", DecimalType(38, 18), True),
    StructField("executed_quantity", DecimalType(38, 18), True),
    StructField("estimated_unit_price", DecimalType(38, 18), True),
    StructField("executed_unit_price", DecimalType(38, 18), True),
    StructField("executed_total_price", DecimalType(38, 18), True),
    StructField("ref_id", StringType(), False),
    StructField("user_pocket_txn_ref_id", StringType(), False),
    StructField("order_type", StringType(), False),
    StructField("status", StringType(), False),
    StructField("transaction_type", StringType(), False),
    StructField("source", StringType(), False),
    StructField("info", StringType(), True),
    StructField("price_info", StringType(), True),
    StructField("is_tax_reported", BooleanType(), True),
    StructField("recurring_order_type", StringType(), True),
    StructField("recurring_transaction_id", StringType(), True),
    StructField("hedging_status", StringType(), False),
    StructField("created", TimestampType(), False),
    StructField("updated", TimestampType(), False),
    StructField("transaction_time", TimestampType(), True),
    StructField("taxes_and_fees", StringType(), True),
])

crypto_currency_wallet_transfers_schema = StructType([
    StructField("id", IntegerType(), False),
    StructField("user_id", IntegerType(), False),
    StructField("account_id", IntegerType(), False),
    StructField("client_id", IntegerType(), False),
    StructField("partner_id", IntegerType(), False),
    StructField("crypto_currency_id", IntegerType(), False),
    StructField("custodian_asset_id", StringType(), True),
    StructField("source_address", StringType(), True),
    StructField("destination_address", StringType(), False),
    StructField("quantity", DecimalType(38, 18), False),
    StructField("total_quantity", DecimalType(38, 18), False),
    StructField("unit_price", DecimalType(38, 18), False),
    StructField("transfer_fee", DecimalType(38, 18), True),
    StructField("network_fee", DecimalType(38, 18), True),
    StructField("block_explorer_url", StringType(), True),
    StructField("status", StringType(), False),
    StructField("transaction_type", StringType(), True),
    StructField("info", StringType(), True),
    StructField("external_response", StringType(), True),
    StructField("source", StringType(), False),
    StructField("vendor_txn_id", StringType(), True),
    StructField("external_status", StringType(), True),
    StructField("created_at", TimestampType(), False),
    StructField("updated_at", TimestampType(), False),
    StructField("network", StringType(), True),
    StructField("risk_score", StringType(), True),
    StructField("transaction_sub_status", StringType(), True),
    StructField("tag", StringType(), True),
    StructField("taxation_fee", DecimalType(38, 18), True),
    StructField("taxation_fee_percentage", DecimalType(38, 18), True),
    StructField("yield_quantity_used", DecimalType(38, 18), True),
    StructField("reward_quantity_used", DecimalType(38, 18), True),
    StructField("network_fee_unit_price", DecimalType(38, 18), True),
    StructField("transaction_time", TimestampType(), True),
    StructField("taxes_and_fees", StringType(), True),
])

crypto_currency_returns_schema = StructType([
    StructField("id", LongType(), True),
    StructField("account_id", LongType(), True),
    StructField("user_id", LongType(), True),
    StructField("crypto_currency_id", LongType(), True),
    StructField("total_quantity", StringType(), True),
    StructField("weighted_cost", StringType(), True),
    StructField("realised_gain", StringType(), True),
    StructField("created", TimestampType(), True),
    StructField("updated", TimestampType(), True)
])

crypto_currency_rewards_disbursal_history_schema = StructType([
    StructField("account_id", LongType(), False),
    StructField("crypto_currency_id", LongType(), False),
    StructField("last_reward_disbursal_date", DateType(), True)
])

crypto_currency_pluangcuan_yields_schema = StructType([
        StructField("id", StringType(), True),
        StructField("crypto_currency_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("yield_type", StringType(), True),
        StructField("yield_rate", StringType(), True),
    ])

crypto_currency_mission_rewards_schema = StructType([
        StructField("id", StringType(), True),
        StructField("crypto_currency_id", StringType(), True),
        StructField("user_id", StringType(), True),
        StructField("account_id", StringType(), True),
        StructField("client_id", StringType(), True),
        StructField("partner_id", StringType(), True),
        StructField("quantity", StringType(), True),
        StructField("unit_price", StringType(), True),
        StructField("total_price", StringType(), True),
        StructField("ref_id", StringType(), True),
        StructField("status", StringType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("created", StringType(), True),
        StructField("updated", StringType(), True),
        StructField("transaction_time", StringType(), True),
        StructField("claim_date", StringType(), True),
        StructField("mission_id", StringType(), True),
        StructField("mission_type", StringType(), True),
    ])