import pytest
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType, StructField, StringType, LongType, DoubleType, TimestampType
from pyspark.sql.functions import col, lit
import pyspark.sql.functions as F

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.jobs.calculations.gss_free_fee import GSSFreeFee


class TestGSSFreeFee:
    """Comprehensive test suite for GSSFreeFee including unit tests, integration tests, and data validation."""



    @pytest.fixture
    def sample_transaction_data(self, spark_session):
        """Sample GSS transaction data for testing."""
        data = [
            # User 1001 - First transaction 10 days ago, eligible
            (1001, 101, 50.0, datetime(2025, 1, 5, 10, 0, 0), datetime(2025, 1, 5, 10, 0, 0), "SUCCESS", 1),
            (1001, 101, 25.0, datetime(2025, 1, 10, 11, 0, 0), datetime(2025, 1, 10, 11, 0, 0), "SUCCESS", 1),
            (1001, 101, 30.0, datetime(2025, 1, 12, 12, 0, 0), datetime(2025, 1, 12, 12, 0, 0), "PARTIALLY_FILLED", 1),
            
            # User 1002 - First transaction 5 days ago, eligible
            (1002, 102, 75.0, datetime(2025, 1, 10, 14, 0, 0), datetime(2025, 1, 10, 14, 0, 0), "SUCCESS", 1),
            (1002, 102, 40.0, datetime(2025, 1, 13, 15, 0, 0), datetime(2025, 1, 13, 15, 0, 0), "PENDING", 1),
            
            # User 1003 - First transaction 35 days ago, not eligible (outside 30-day window)
            (1003, 103, 100.0, datetime(2024, 12, 11, 16, 0, 0), datetime(2024, 12, 11, 16, 0, 0), "SUCCESS", 1),
            (1003, 103, 50.0, datetime(2025, 1, 14, 17, 0, 0), datetime(2025, 1, 14, 17, 0, 0), "SUCCESS", 1),
            
            # User 1004 - Different partner_id, should be filtered out
            (1004, 104, 60.0, datetime(2025, 1, 8, 18, 0, 0), datetime(2025, 1, 8, 18, 0, 0), "SUCCESS", 2),
            
            # User 1005 - Failed transaction, should be filtered out
            (1005, 105, 80.0, datetime(2025, 1, 9, 19, 0, 0), datetime(2025, 1, 9, 19, 0, 0), "FAILED", 1),
            
            # User 1006 - Zero account_id, should be filtered out in final result
            (0, 106, 90.0, datetime(2025, 1, 7, 20, 0, 0), datetime(2025, 1, 7, 20, 0, 0), "SUCCESS", 1)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        return spark_session.createDataFrame(data, schema)

    # UNIT TESTS
    @patch('src.jobs.calculations.gss_free_fee.SparkUtils')
    @patch('src.jobs.calculations.gss_free_fee.Operations')
    @patch('src.jobs.calculations.gss_free_fee.IOUtils')
    @patch('src.jobs.calculations.gss_free_fee.get_logger')
    def test_init(self, mock_logger, mock_io_utils, mock_operations, mock_spark_utils, mock_gss_free_fee_config):
        """Test GSSFreeFee initialization."""
        mock_spark = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark
        mock_logger.return_value = Mock()

        gss_free_fee = GSSFreeFee(mock_gss_free_fee_config)

        # Test configuration parameters
        assert gss_free_fee.bucket_path == "s3a://test-bucket"
        assert gss_free_fee.pluang_partner_id == 1
        assert gss_free_fee.consider_transaction_date == "2025-01-01T00:00:00.000Z"
        assert gss_free_fee.free_fee_capping == 100000.0
        assert gss_free_fee.write_free_fee_path == "gss_free_fees_output"
        assert gss_free_fee.t_1 == "2025-01-15"

        # Test utility objects initialization
        mock_spark_utils.assert_called_once_with("gss_free_fees")
        mock_operations.assert_called_once_with(mock_spark)
        mock_io_utils.assert_called_once_with(mock_spark, mock_config)

    @patch('src.jobs.calculations.gss_free_fee.SparkUtils')
    @patch('src.jobs.calculations.gss_free_fee.Operations')
    @patch('src.jobs.calculations.gss_free_fee.IOUtils')
    @patch('src.jobs.calculations.gss_free_fee.get_logger')
    def test_load_gss_transaction_base(self, mock_logger, mock_io_utils, mock_operations,
                                     mock_spark_utils, mock_gss_free_fee_config, sample_transaction_data):
        """Test loading and filtering of base transaction data."""
        mock_spark = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark
        mock_logger.return_value = Mock()
        
        mock_io_utils_instance = Mock()
        mock_io_utils_instance.read_parquet_data.return_value = sample_transaction_data
        mock_io_utils.return_value = mock_io_utils_instance

        gss_free_fee = GSSFreeFee(mock_config)
        result = gss_free_fee.load_gss_transaction_base()

        # Verify path construction
        expected_path = "s3a://test-bucket/snapshots/global_stock_transactions/dt=2025-01-15/*"
        mock_io_utils_instance.read_parquet_data.assert_called_once_with(expected_path)

        # Verify filtering and column selection
        result_data = result.collect()
        # From sample data:
        # 1001(3 txns) - SUCCESS, SUCCESS, PARTIALLY_FILLED, partner_id=1 ✓
        # 1002(2 txns) - SUCCESS, PENDING, partner_id=1 ✓
        # 1003(2 txns) - SUCCESS, SUCCESS, partner_id=1 ✓
        # 1004(1 txn) - SUCCESS, partner_id=2 ✗ (filtered out)
        # 1005(1 txn) - FAILED, partner_id=1 ✗ (filtered out)
        # 1006(1 txn) - SUCCESS, partner_id=1 ✓
        # Total should be 8 rows (3+2+2+1)
        assert len(result_data) == 8

        # Verify columns
        expected_columns = ["account_id", "user_id", "waived_off_fee", "created", "transaction_time"]
        assert result.columns == expected_columns

    def test_get_first_txn_users_last_30_days(self, spark_session, sample_transaction_data, mock_config):
        """Test identification of users with first transaction in last 30 days."""
        # Create a mock GSSFreeFee instance with minimal setup
        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):
            
            gss_free_fee = GSSFreeFee(mock_config)
            result = gss_free_fee.get_first_txn_users_last_30_days(sample_transaction_data)
            
            result_data = result.collect()
            
            # Should include users 1001, 1002, 1006 (first txn within 30 days)
            # Should exclude user 1003 (first txn 35 days ago)
            user_ids = [row.user_id for row in result_data]
            assert 101 in user_ids  # User 1001
            assert 102 in user_ids  # User 1002
            assert 106 in user_ids  # User 1006
            assert 103 not in user_ids  # User 1003 (outside 30-day window)

    @patch('src.jobs.calculations.gss_free_fee.DateUtils')
    def test_find_gss_transaction(self, mock_date_utils, spark_session, sample_transaction_data, mock_config):
        """Test GSS transaction filtering by date window."""
        mock_date_utils.get_jkt_timestamp.return_value = datetime(2025, 1, 15, 8, 0, 0)
        
        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):
            
            gss_free_fee = GSSFreeFee(mock_config)
            result = gss_free_fee.find_gss_transaction(sample_transaction_data)
            
            # Verify DateUtils was called
            mock_date_utils.get_jkt_timestamp.assert_called_once_with(0)
            
            # Verify column renaming
            assert "user_id_dup" in result.columns
            assert "user_id" not in result.columns
            assert "created" not in result.columns  # Should be dropped

    def test_find_eligible_user(self, spark_session, mock_config):
        """Test joining first-time users with transactions and fee aggregation."""
        # Create test data for first_txn_users
        first_txn_data = [
            (101, 1001, datetime(2025, 1, 5, 10, 0, 0)),
            (102, 1002, datetime(2025, 1, 10, 14, 0, 0))
        ]
        first_txn_schema = StructType([
            StructField("user_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("first_txn_time", TimestampType(), True)
        ])
        first_txn_users = spark_session.createDataFrame(first_txn_data, first_txn_schema)

        # Create test data for gss_transaction - note the join is on user_id
        gss_txn_data = [
            (101, 50.0, datetime(2025, 1, 5, 10, 0, 0)),  # user_id_dup = 101 (matches first_txn user_id)
            (101, 25.0, datetime(2025, 1, 10, 11, 0, 0)),  # user_id_dup = 101
            (102, 75.0, datetime(2025, 1, 10, 14, 0, 0))   # user_id_dup = 102
        ]
        gss_txn_schema = StructType([
            StructField("user_id_dup", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("transaction_time", TimestampType(), True)
        ])
        gss_transaction = spark_session.createDataFrame(gss_txn_data, gss_txn_schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):

            gss_free_fee = GSSFreeFee(mock_config)
            result = gss_free_fee.find_eligible_user(first_txn_users, gss_transaction)

            result_data = result.collect()

            # Verify aggregation
            user_101_row = next((row for row in result_data if row.user_id == 101), None)
            assert user_101_row is not None
            assert user_101_row.total_gss_fee_waived == 75.0  # 50.0 + 25.0

            user_102_row = next((row for row in result_data if row.user_id == 102), None)
            assert user_102_row is not None
            assert user_102_row.total_gss_fee_waived == 75.0

    @patch('src.jobs.calculations.gss_free_fee.SparkUtils')
    @patch('src.jobs.calculations.gss_free_fee.Operations')
    @patch('src.jobs.calculations.gss_free_fee.IOUtils')
    @patch('src.jobs.calculations.gss_free_fee.get_logger')
    def test_build_mongo_config(self, mock_logger, mock_io_utils, mock_operations, 
                               mock_spark_utils, mock_config):
        """Test MongoDB configuration building."""
        mock_spark = Mock()
        mock_spark_utils.return_value.create_spark_session.return_value = mock_spark
        mock_logger.return_value = Mock()
        
        mock_io_utils_instance = Mock()
        mock_io_utils_instance.get_mongo_connection_string.return_value = "*******************************************"
        mock_io_utils.return_value = mock_io_utils_instance

        gss_free_fee = GSSFreeFee(mock_config)
        mongo_config = gss_free_fee.build_mongo_config()

        # Verify structure
        assert "uri" in mongo_config
        assert "collection" in mongo_config
        assert "batch_size" in mongo_config
        assert "mode" in mongo_config

        # Verify values
        assert mongo_config["collection"] == "user_price_tiering"
        assert mongo_config["batch_size"] == "500"
        assert mongo_config["mode"] == "append"
        assert mongo_config["uri"] == "*******************************************"

    # DATA VALIDATION TESTS
    def test_null_value_handling(self, spark_session, mock_config):
        """Test handling of null values in transaction data."""
        # Create data with null values
        data = [
            (None, 101, 50.0, datetime(2025, 1, 5, 10, 0, 0), datetime(2025, 1, 5, 10, 0, 0), "SUCCESS", 1),
            (1001, None, 25.0, datetime(2025, 1, 10, 11, 0, 0), datetime(2025, 1, 10, 11, 0, 0), "SUCCESS", 1),
            (1002, 102, None, datetime(2025, 1, 12, 12, 0, 0), datetime(2025, 1, 12, 12, 0, 0), "SUCCESS", 1),
            (1003, 103, 30.0, None, datetime(2025, 1, 13, 13, 0, 0), "SUCCESS", 1),
            (1004, 104, 40.0, datetime(2025, 1, 14, 14, 0, 0), None, "SUCCESS", 1)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        null_data = spark_session.createDataFrame(data, schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):
            
            gss_free_fee = GSSFreeFee(mock_config)
            
            # Test that the job handles null values gracefully
            try:
                result = gss_free_fee.get_first_txn_users_last_30_days(null_data)
                # Should not crash and should handle nulls appropriately
                result_count = result.count()
                assert result_count >= 0  # Should not crash
            except Exception as e:
                pytest.fail(f"Job should handle null values gracefully, but failed with: {str(e)}")

    def test_negative_value_handling(self, spark_session, mock_config):
        """Test handling of negative values in transaction data."""
        data = [
            (-1, 101, 50.0, datetime(2025, 1, 5, 10, 0, 0), datetime(2025, 1, 5, 10, 0, 0), "SUCCESS", 1),
            (1001, -101, 25.0, datetime(2025, 1, 10, 11, 0, 0), datetime(2025, 1, 10, 11, 0, 0), "SUCCESS", 1),
            (1002, 102, -50.0, datetime(2025, 1, 12, 12, 0, 0), datetime(2025, 1, 12, 12, 0, 0), "SUCCESS", 1),
            (1003, 103, 30.0, datetime(2025, 1, 13, 13, 0, 0), datetime(2025, 1, 13, 13, 0, 0), "SUCCESS", -1)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        negative_data = spark_session.createDataFrame(data, schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):
            
            gss_free_fee = GSSFreeFee(mock_config)
            
            # Test load_gss_transaction_base with negative values
            with patch.object(gss_free_fee.io_utils, 'read_parquet_data', return_value=negative_data):
                result = gss_free_fee.load_gss_transaction_base()

                # Should filter out negative partner_id
                result_data = result.collect()
                # Note: partner_id column is not in the selected columns, so we check the data was filtered
                # by checking that only 3 rows remain (excluding the one with partner_id=-1)
                assert len(result_data) == 3

    def test_extreme_value_handling(self, spark_session, mock_config):
        """Test handling of extreme values in transaction data."""
        import sys

        data = [
            (sys.maxsize, 101, 50.0, datetime(2025, 1, 5, 10, 0, 0), datetime(2025, 1, 5, 10, 0, 0), "SUCCESS", 1),
            (1001, sys.maxsize, 25.0, datetime(2025, 1, 10, 11, 0, 0), datetime(2025, 1, 10, 11, 0, 0), "SUCCESS", 1),
            (1002, 102, float('inf'), datetime(2025, 1, 12, 12, 0, 0), datetime(2025, 1, 12, 12, 0, 0), "SUCCESS", 1),
            (1003, 103, 1e10, datetime(2025, 1, 13, 13, 0, 0), datetime(2025, 1, 13, 13, 0, 0), "SUCCESS", 1)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        extreme_data = spark_session.createDataFrame(data, schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):

            gss_free_fee = GSSFreeFee(mock_config)

            # Test that extreme values don't crash the processing
            try:
                result = gss_free_fee.get_first_txn_users_last_30_days(extreme_data)
                result_count = result.count()
                assert result_count >= 0
            except Exception as e:
                pytest.fail(f"Job should handle extreme values gracefully, but failed with: {str(e)}")

    def test_invalid_status_filtering(self, spark_session, mock_config):
        """Test filtering of invalid transaction statuses."""
        data = [
            (1001, 101, 50.0, datetime(2025, 1, 5, 10, 0, 0), datetime(2025, 1, 5, 10, 0, 0), "SUCCESS", 1),
            (1002, 102, 25.0, datetime(2025, 1, 10, 11, 0, 0), datetime(2025, 1, 10, 11, 0, 0), "FAILED", 1),
            (1003, 103, 30.0, datetime(2025, 1, 12, 12, 0, 0), datetime(2025, 1, 12, 12, 0, 0), "CANCELLED", 1),
            (1004, 104, 40.0, datetime(2025, 1, 13, 13, 0, 0), datetime(2025, 1, 13, 13, 0, 0), "INVALID_STATUS", 1),
            (1005, 105, 35.0, datetime(2025, 1, 14, 14, 0, 0), datetime(2025, 1, 14, 14, 0, 0), "PARTIALLY_FILLED", 1)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        status_data = spark_session.createDataFrame(data, schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):

            gss_free_fee = GSSFreeFee(mock_config)

            with patch.object(gss_free_fee.io_utils, 'read_parquet_data', return_value=status_data):
                result = gss_free_fee.load_gss_transaction_base()

                result_data = result.collect()
                # Note: status column is not in the selected columns, so we check filtering by row count
                # Should only include SUCCESS and PARTIALLY_FILLED (2 rows)
                assert len(result_data) == 2  # Only SUCCESS and PARTIALLY_FILLED transactions

    # INTEGRATION TESTS
    @patch('src.jobs.calculations.gss_free_fee.SparkUtils')
    @patch('src.jobs.calculations.gss_free_fee.Operations')
    @patch('src.jobs.calculations.gss_free_fee.IOUtils')
    @patch('src.jobs.calculations.gss_free_fee.get_logger')
    @patch('src.jobs.calculations.gss_free_fee.DateUtils')
    def test_end_to_end_processing(self, mock_date_utils, mock_logger, mock_io_utils,
                                 mock_operations, mock_spark_utils, mock_config, sample_transaction_data):
        """Test complete end-to-end processing workflow."""
        # Setup mocks
        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_logger.return_value = Mock()

        mock_io_utils_instance = Mock()
        mock_io_utils_instance.read_parquet_data.return_value = sample_transaction_data
        mock_io_utils_instance.write_csv_file = Mock()
        mock_io_utils_instance.write_dataset_to_mongo = Mock()
        mock_io_utils_instance.get_mongo_connection_string.return_value = "*******************************************"
        mock_io_utils.return_value = mock_io_utils_instance

        mock_date_utils.get_jkt_timestamp.return_value = datetime(2025, 1, 15, 8, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)

        # Create and run the job
        gss_free_fee = GSSFreeFee(mock_config)
        gss_free_fee.start_processing()

        # Verify that all major steps were executed
        mock_io_utils_instance.read_parquet_data.assert_called_once()
        mock_io_utils_instance.write_csv_file.assert_called_once()  # CSV output
        mock_io_utils_instance.write_dataset_to_mongo.assert_called_once()  # MongoDB write

        # Verify CSV write call
        csv_call = mock_io_utils_instance.write_csv_file.call_args_list[0]
        csv_path = csv_call[0][1]
        assert "gss_free_fees_output" in csv_path
        assert "dt=2025-01-15" in csv_path

        # Verify MongoDB write call
        mongo_call = mock_io_utils_instance.write_dataset_to_mongo.call_args_list[0]
        mongo_kwargs = mongo_call[1]
        assert mongo_kwargs["asset_name"] == "gss_free_fees"
        assert mongo_kwargs["write_format"] == "update"

    def test_empty_data_handling(self, spark_session, mock_config):
        """Test handling of empty input datasets."""
        # Create empty DataFrame with correct schema
        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        empty_data = spark_session.createDataFrame([], schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):

            gss_free_fee = GSSFreeFee(mock_config)

            # Test each method with empty data
            result1 = gss_free_fee.get_first_txn_users_last_30_days(empty_data)
            assert result1.count() == 0

            result2 = gss_free_fee.find_gss_transaction(empty_data)
            assert result2.count() == 0

    def test_date_filtering_edge_cases(self, spark_session, mock_config):
        """Test edge cases in date filtering logic."""
        # Create data with transactions exactly at 30-day boundary
        base_date = datetime(2025, 1, 15)  # t_1 date
        exactly_30_days_ago = base_date - timedelta(days=30)
        exactly_29_days_ago = base_date - timedelta(days=29)
        exactly_31_days_ago = base_date - timedelta(days=31)

        data = [
            (1001, 101, 50.0, exactly_30_days_ago, exactly_30_days_ago, "SUCCESS", 1),  # Exactly 30 days
            (1002, 102, 25.0, exactly_29_days_ago, exactly_29_days_ago, "SUCCESS", 1),  # 29 days (eligible)
            (1003, 103, 30.0, exactly_31_days_ago, exactly_31_days_ago, "SUCCESS", 1),  # 31 days (not eligible)
        ]

        schema = StructType([
            StructField("account_id", LongType(), True),
            StructField("user_id", LongType(), True),
            StructField("waived_off_fee", DoubleType(), True),
            StructField("created", TimestampType(), True),
            StructField("transaction_time", TimestampType(), True),
            StructField("status", StringType(), True),
            StructField("partner_id", LongType(), True)
        ])

        edge_case_data = spark_session.createDataFrame(data, schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'):

            gss_free_fee = GSSFreeFee(mock_config)
            result = gss_free_fee.get_first_txn_users_last_30_days(edge_case_data)

            result_data = result.collect()
            user_ids = [row.user_id for row in result_data]

            # 29 days should be included, 31 days should be excluded
            # 30 days exactly should be excluded (< 30, not <= 30)
            assert 102 in user_ids  # 29 days ago
            assert 101 not in user_ids  # exactly 30 days ago
            assert 103 not in user_ids  # 31 days ago

    def test_fee_capping_logic(self, spark_session, mock_config):
        """Test fee capping logic in the final output."""
        # Create test data with fees above and below capping threshold
        user_fees_data = [
            (101, 1001, 50000.0),   # Below capping (100000.0)
            (102, 1002, 150000.0),  # Above capping
            (103, 1003, 100000.0),  # Exactly at capping
        ]

        schema = StructType([
            StructField("user_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("total_gss_fee_waived", DoubleType(), True)
        ])

        user_fees = spark_session.createDataFrame(user_fees_data, schema)

        with patch('src.jobs.calculations.gss_free_fee.SparkUtils'), \
             patch('src.jobs.calculations.gss_free_fee.Operations'), \
             patch('src.jobs.calculations.gss_free_fee.IOUtils'), \
             patch('src.jobs.calculations.gss_free_fee.get_logger'), \
             patch('src.jobs.calculations.gss_free_fee.DateUtils') as mock_date_utils:

            mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)

            gss_free_fee = GSSFreeFee(mock_config)

            # Apply capping logic (similar to what's done in start_processing)
            result = user_fees.withColumn(
                "gss_fee_max_cap_reached",
                F.when(col("total_gss_fee_waived") > gss_free_fee.free_fee_capping, lit(True)).otherwise(False)
            )

            result_data = result.collect()

            # Verify capping flags
            user_101 = next((row for row in result_data if row.user_id == 101), None)
            assert user_101.gss_fee_max_cap_reached == False  # Below capping

            user_102 = next((row for row in result_data if row.user_id == 102), None)
            assert user_102.gss_fee_max_cap_reached == True   # Above capping

            user_103 = next((row for row in result_data if row.user_id == 103), None)
            assert user_103.gss_fee_max_cap_reached == False  # Exactly at capping (not greater than)

    @patch('src.jobs.calculations.gss_free_fee.SparkUtils')
    @patch('src.jobs.calculations.gss_free_fee.Operations')
    @patch('src.jobs.calculations.gss_free_fee.IOUtils')
    @patch('src.jobs.calculations.gss_free_fee.get_logger')
    def test_run_method(self, mock_logger, mock_io_utils, mock_operations, mock_spark_utils, mock_config):
        """Test the main run method."""
        mock_spark = Mock()
        mock_spark_utils_instance = Mock()
        mock_spark_utils_instance.create_spark_session.return_value = mock_spark
        mock_spark_utils.return_value = mock_spark_utils_instance
        mock_logger.return_value = Mock()
        mock_io_utils.return_value = Mock()

        gss_free_fee = GSSFreeFee(mock_config)

        # Mock start_processing to avoid complex setup
        gss_free_fee.start_processing = Mock()

        # Test run method
        gss_free_fee.run()

        # Verify workflow
        gss_free_fee.start_processing.assert_called_once()
        mock_spark_utils_instance.stop_spark.assert_called_once_with(mock_spark)

    def test_zero_account_id_filtering(self, spark_session, mock_config):
        """Test that accounts with account_id = 0 are filtered out."""
        user_fees_data = [
            (101, 1001, 50.0),   # Valid account
            (102, 0, 75.0),      # Zero account_id - should be filtered
            (103, 1003, 25.0),   # Valid account
        ]

        schema = StructType([
            StructField("user_id", LongType(), True),
            StructField("account_id", LongType(), True),
            StructField("total_gss_fee_waived", DoubleType(), True)
        ])

        user_fees = spark_session.createDataFrame(user_fees_data, schema)

        # Apply the same filtering as in start_processing
        filtered_result = user_fees.filter(col("account_id") != 0)
        result_data = filtered_result.collect()

        # Verify zero account_id is filtered out
        account_ids = [row.account_id for row in result_data]
        assert 0 not in account_ids
        assert 1001 in account_ids
        assert 1003 in account_ids
        assert len(result_data) == 2  # Only 2 valid accounts
