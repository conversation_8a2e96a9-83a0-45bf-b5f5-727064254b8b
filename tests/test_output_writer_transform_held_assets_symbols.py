"""
Unit tests for CohortOutputWriter._transform_held_assets_symbols.
"""

import pytest
from unittest.mock import Mock
from pyspark.sql.types import StructType, StructField, StringType, LongType

from src.jobs.cohorting.writers.cohort_output_writer import CohortOutputWriter


@pytest.fixture
def mock_config_small_limit():
    # Use a very small limit to force segmentation in test
    return {
        "cohorting": {
            "clevertap_max_char_limit": 10
        }
    }


@pytest.fixture
def input_positions_df(spark_session):
    # Build positions that will require multiple segments when joined with commas
    data = [
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "AAPL"},
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "MSFT"},
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "NVDA"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "BTC"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "ETH"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "SOL"},
    ]
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_symbol", StringType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


def test_transform_segments_respect_limit(spark_session, mock_config_small_limit, input_positions_df):
    logger = Mock()
    writer = CohortOutputWriter(spark_session, mock_config_small_limit, logger)

    out = writer._transform_held_assets_cohort_data(input_positions_df, max_segments=4)
    cols = out.columns
    # Validate required columns exist
    for c in [
        "user_id", "account_id",
        "global_stocks_held_asset_symbols_1",
        "global_stocks_held_asset_symbols_2",
        "crypto_currency_held_asset_symbols_1",
        "crypto_currency_held_asset_symbols_2",
    ]:
        assert c in cols

    rows = out.collect()
    assert len(rows) == 1
    row = rows[0]

    # Ensure no segment exceeds the configured limit
    limit = mock_config_small_limit["cohorting"]["clevertap_max_char_limit"]
    for c in cols:
        val = getattr(row, c)
        if isinstance(val, str):
            assert len(val) <= limit

    # Validate that combined tokens are covered across segments (order-insensitive)
    gss_parts = [getattr(row, f"global_stocks_held_asset_symbols_{i}") for i in range(1, 5)]
    gss_tokens = set()
    for p in gss_parts:
        if p and p != "0":  # Skip "0" values (empty segments)
            gss_tokens.update(p.split(","))
    assert gss_tokens == {"AAPL", "MSFT", "NVDA"}

    crypto_parts = [getattr(row, f"crypto_currency_held_asset_symbols_{i}") for i in range(1, 5)]
    crypto_tokens = set()
    for p in crypto_parts:
        if p and p != "0":  # Skip "0" values (empty segments)
            crypto_tokens.update(p.split(","))
    assert crypto_tokens == {"BTC", "ETH", "SOL"}
    
    # Ensure empty segments are "0" not null
    for i in range(1, 5):
        gss_val = getattr(row, f"global_stocks_held_asset_symbols_{i}")
        crypto_val = getattr(row, f"crypto_currency_held_asset_symbols_{i}")
        assert gss_val is not None, f"global_stocks segment {i} should not be null"
        assert crypto_val is not None, f"crypto_currency segment {i} should not be null"


@pytest.fixture
def input_single_asset_type_df(spark_session):
    """User with only global_stocks, no crypto holdings."""
    data = [
        {"account_id": 2, "user_id": 20, "asset_type": "global_stocks", "asset_symbol": "TSLA"},
        {"account_id": 2, "user_id": 20, "asset_type": "global_stocks", "asset_symbol": "GOOGL"},
    ]
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_symbol", StringType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


def test_transform_single_asset_type_fills_zeros(spark_session, mock_config_small_limit, input_single_asset_type_df):
    """Test that users with only one asset type get '0' for missing asset type columns."""
    logger = Mock()
    writer = CohortOutputWriter(spark_session, mock_config_small_limit, logger)

    out = writer._transform_held_assets_cohort_data(input_single_asset_type_df, max_segments=4)
    
    rows = out.collect()
    assert len(rows) == 1
    row = rows[0]
    
    # User has global_stocks
    gss_1 = getattr(row, "global_stocks_held_asset_symbols_1")
    assert gss_1 is not None
    assert gss_1 != "0"  # Should contain actual symbols
    assert "GOOGL" in gss_1 or "TSLA" in gss_1
    
    # User does NOT have crypto_currency, so all crypto segments should be "0"
    for i in range(1, 5):
        crypto_val = getattr(row, f"crypto_currency_held_asset_symbols_{i}")
        assert crypto_val is not None, f"crypto_currency segment {i} should not be null"
        assert crypto_val == "0", f"crypto_currency segment {i} should be '0' for users without crypto holdings"


@pytest.fixture
def input_mixed_users_df(spark_session):
    """Multiple users with different asset type combinations."""
    data = [
        # User 1: both asset types
        {"account_id": 1, "user_id": 10, "asset_type": "global_stocks", "asset_symbol": "AAPL"},
        {"account_id": 1, "user_id": 10, "asset_type": "crypto_currency", "asset_symbol": "BTC"},
        # User 2: only global_stocks
        {"account_id": 2, "user_id": 20, "asset_type": "global_stocks", "asset_symbol": "MSFT"},
        # User 3: only crypto_currency
        {"account_id": 3, "user_id": 30, "asset_type": "crypto_currency", "asset_symbol": "ETH"},
    ]
    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("asset_type", StringType(), True),
        StructField("asset_symbol", StringType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


def test_transform_mixed_users_no_nulls(spark_session, mock_config_small_limit, input_mixed_users_df):
    """Test that all users get '0' for asset types they don't hold."""
    logger = Mock()
    writer = CohortOutputWriter(spark_session, mock_config_small_limit, logger)

    out = writer._transform_held_assets_cohort_data(input_mixed_users_df, max_segments=4)
    
    rows = out.collect()
    assert len(rows) == 3
    
    for row in rows:
        user_id = row.user_id
        
        # Check all segment columns are not null
        for i in range(1, 5):
            gss_val = getattr(row, f"global_stocks_held_asset_symbols_{i}")
            crypto_val = getattr(row, f"crypto_currency_held_asset_symbols_{i}")
            
            assert gss_val is not None, f"User {user_id} global_stocks segment {i} should not be null"
            assert crypto_val is not None, f"User {user_id} crypto_currency segment {i} should not be null"
            
        # User 10 has both
        if user_id == 10:
            assert row.global_stocks_held_asset_symbols_1 != "0"
            assert row.crypto_currency_held_asset_symbols_1 != "0"
        # User 20 has only global_stocks
        elif user_id == 20:
            assert row.global_stocks_held_asset_symbols_1 != "0"
            assert row.crypto_currency_held_asset_symbols_1 == "0"
        # User 30 has only crypto
        elif user_id == 30:
            assert row.global_stocks_held_asset_symbols_1 == "0"
            assert row.crypto_currency_held_asset_symbols_1 != "0"