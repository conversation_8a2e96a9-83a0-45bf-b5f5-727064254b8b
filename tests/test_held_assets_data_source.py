"""
Unit tests for HeldAssetsDataSource.
"""

import pytest
from unittest.mock import Mock, patch
from pyspark.sql.types import StructType, StructField, StringType, LongType, DoubleType

from src.jobs.cohorting.data_sources.held_assets_data_source import HeldAssetsDataSource


@pytest.fixture
def mock_config():
    return {
        "bucket_path": "s3://test-bucket",
        "t_1": "2025-10-14",
    }


@pytest.fixture
def sample_gss_returns_df(spark_session):
    data = [
        {"global_stock_id": 1001, "account_id": 2001, "user_id": 3001, "total_quantity": 10.0},
        {"global_stock_id": 1002, "account_id": 2001, "user_id": 3001, "total_quantity": 0.0},  # filtered
    ]
    schema = StructType([
        StructField("global_stock_id", LongType(), True),
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("total_quantity", DoubleType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_crypto_returns_df(spark_session):
    data = [
        {"crypto_currency_id": 5001, "account_id": 2001, "user_id": 3001, "total_quantity": 5.0},
        {"crypto_currency_id": 5002, "account_id": 2002, "user_id": 3002, "total_quantity": 0.0},  # filtered
    ]
    schema = StructType([
        StructField("crypto_currency_id", LongType(), True),
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("total_quantity", DoubleType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_gss_assets_df(spark_session):
    data = [
        {"id": 1001, "pluang_company_code": "AAPL"},
        {"id": 1002, "pluang_company_code": "GOOGL"},
    ]
    schema = StructType([
        StructField("id", LongType(), True),
        StructField("pluang_company_code", StringType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_crypto_assets_df(spark_session):
    data = [
        {"id": 5001, "symbol": "BTC"},
        {"id": 5002, "symbol": "ETH"},
    ]
    schema = StructType([
        StructField("id", LongType(), True),
        StructField("symbol", StringType(), True),
    ])
    return spark_session.createDataFrame(data, schema)


class TestHeldAssetsDataSource:
    @patch('src.jobs.cohorting.data_sources.base_data_source.IOUtils')
    @patch('src.jobs.cohorting.data_sources.held_assets_data_source.AssetUtils')
    def test_read_unified_positions(self, mock_asset_utils, mock_io_utils, mock_config, spark_session,
                                    sample_gss_returns_df, sample_crypto_returns_df,
                                    sample_gss_assets_df, sample_crypto_assets_df):
        logger = Mock()

        # IO mocks
        io_instance = Mock()
        mock_io_utils.return_value = io_instance
        io_instance.read_csv_file.side_effect = [sample_gss_returns_df, sample_crypto_returns_df]

        # Asset utils mocks
        asset_instance = Mock()
        mock_asset_utils.return_value = asset_instance
        asset_instance.get_global_stock_assets.return_value = sample_gss_assets_df
        asset_instance.get_crypto_assets.return_value = sample_crypto_assets_df

        ds = HeldAssetsDataSource(spark_session, mock_config, logger)

        result = ds.read({"partition_date": mock_config["t_1"], "assets": ["gss", "crypto"]}, [
            "account_id", "user_id", "asset_type", "asset_symbol"
        ])

        assert result is not None
        rows = result.collect()

        # Debug: Print the actual result
        print(f"DEBUG: Result has {len(rows)} rows")
        for i, row in enumerate(rows):
            print(f"DEBUG: Row {i}: {row}")

        # Print schema
        print(f"DEBUG: Schema: {result.schema}")

        # Expect 2 rows (one gss for AAPL, one crypto for BTC). Zero qty rows filtered out
        assert len(rows) == 2
        # Validate schema/values
        for r in rows:
            assert r.account_id in [2001]
            assert r.user_id in [3001]
            assert r.asset_type in ["global_stocks", "crypto_currency"]
        symbols = {r.asset_symbol for r in rows}
        assert symbols == {"AAPL", "BTC"}



