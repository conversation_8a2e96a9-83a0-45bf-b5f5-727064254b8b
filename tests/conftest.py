import pytest
import os
import sys
from datetime import datetime, timedelta
from unittest.mock import Mock, MagicMock, patch
from pyspark.sql import SparkSession
from pyspark.sql.types import *
from pyspark.sql import functions as F

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Initialize logger for tests
from src.utils.custom_logger import init_logger

init_logger('test')


@pytest.fixture(scope="session")
def spark_session():
    """Create a Spark session for testing."""
    spark = SparkSession.builder \
        .appName("FlashGamesPnL_Test") \
        .master("local[2]") \
        .config("spark.sql.warehouse.dir", "/tmp/spark-warehouse") \
        .config("spark.sql.adaptive.enabled", "false") \
        .config("spark.sql.adaptive.coalescePartitions.enabled", "false") \
        .config("spark.driver.bindAddress", "127.0.0.1") \
        .config("spark.driver.host", "127.0.0.1") \
        .getOrCreate()

    # Set log level to reduce noise
    spark.sparkContext.setLogLevel("WARN")

    yield spark
    spark.stop()


@pytest.fixture
def mock_config():
    """Mock configuration for FlashGamesPnL."""
    return {
        "bucket_path": "s3a://test-bucket",
        "snapshot_path": "snapshots",
        "batches": {
            "batch_file_path": "batches",
            "flash_games_batch_file_path": "flash_games_batches",
            "all_transaction_file_path": "all_transactions"
        },
        "trading_competition": {
            "id": "TC_2025_Q1",
            "start_time": "2025-01-01 00:00:00.000",
            "frequency": 24
        },
        "aum_tier_upgrade": {
            "tier_snapshot_path": "tier_snapshots"
        },
        "flash_games": {
            "game_1": {
                "start_ts": "2025-01-15 10:00:00.000",
                "end_ts": "2025-01-15 18:00:00.000",
                "schedule_end_ts": "2025-01-15 19:00:00.000",
                "assets": {
                    "global_stocks": [1, 2, 3],
                    "crypto_currency": [101, 102],
                    "global_stock_options": [],
                    "global_stock_with_leverage": []
                }
            }
        },
        "buy_types": ["BUY", "AIRDROP_BUY"],
        "sell_types": ["SELL", "AIRDROP_SELL"],
        "bootstrap_servers": "localhost:9092",
        "kafka_topics": {
            "global_stock_topic": "global_stocks"
        },
        "niv_path": "niv",
        "gtv_path": "gtv",
        "flash_games_assets_path": "flash_games_assets",
        "flash_games_pnl_path": "flash_games_pnl",
        "data_store": {
            "reporting_mongo": {
                "host": "localhost",
                "port": 27017,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            },
            "flash_game": {
                "collection": "flash_game_leaderboard"
            }
        },
        "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0),  # During flash game
        "offset": 0,
        "execution_time": "jkt_day_end"
    }


@pytest.fixture
def mock_transaction_config():
    """Mock configuration for TransactionTransformer."""
    return {
        "bucket_path": "s3a://test-bucket",
        "snapshot_path": "snapshots",
        "batches": {
            "all_transaction_file_path": "all_transactions"
        },
        "trading_competition": {
            "start_time": "2025-01-01 00:00:00.000"
        },
        "aum_tier_upgrade": {
            "tier_snapshot_path": "tier_snapshots"
        },
        "crypto_rebrand_txn_path": "crypto_rebrand_transactions",
        "start_asset_position_path": "start_positions",
        "global_stock_splits_path": "stock_splits",
        "pluang_partner_id": 1,
        "usdt_coin_id": 825,
        "prices": {
            "forex": {"price_path": "forex_prices"},
            "gold": {"price_path": "gold_prices"},
            "global_stock": {"price_path": "global_stock_prices"},
            "crypto_currency": {"price_path": "crypto_prices"},
            "crypto_currency_futures": {"price_path": "crypto_futures_prices"},
            "fund": {"price_path": "fund_prices"},
            "global_stock_options": {
                "price_path": "options_prices",
                "price_snapshot_folder": "snapshots"
            }
        },
        "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0)
    }


@pytest.fixture
def mock_aum_tier_upgrade_config():
    """Mock configuration for AUMTierUpgradeProcessor."""
    return {
        "bucket_path": "s3a://test-bucket",
        "trading_competition": {
            "offset": 0,
            "frequency": 24
        },
        "aum_tier_upgrade": {
            "aum_file_path": "aum_data",
            "tier_event_snapshot_path": "tier_events",
            "tier_snapshot_path": "tier_snapshots",
            "tier_upgrade_range": [
                {"tier": "BRONZE", "min_aum": 0, "max_aum": 1000000},
                {"tier": "SILVER", "min_aum": 1000000, "max_aum": 5000000},
                {"tier": "GOLD", "min_aum": 5000000, "max_aum": 25000000},
                {"tier": "CHALLENGER", "min_aum": 25000000, "max_aum": None},
                {"tier": "LEGEND", "min_aum": 100000000, "max_aum": None}
            ],
            "mongo_collection": "aum_tier_upgrade"
        },
        "kafka_topics": {
            "clevertap_events_topic": "clevertap_events"
        },
        "data_store": {
            "reporting_mongo": {
                "host": "localhost",
                "port": 27017,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            },
            "trading_competition": {
                "collection": "trading_competition"
            }
        },
        "trading_competition_mongo_collection": "trading_competition",
        "utc_cutoff_ts": datetime(2025, 1, 15, 14, 0, 0)
    }


@pytest.fixture
def mock_logger():
    """Mock logger."""
    logger = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    return logger


@pytest.fixture
def mock_cohorting_config():
    """Mock configuration for Cohorting Engine."""
    return {
        "bucket_path": "s3a://test-bucket",
        "offset": 0,
        "t_1": "2025-10-15",
        "t_2": "2025-10-14",
        "bq_app_events_db": "test_bq_dataset",
        "data_store": {
            "reporting_mongo": {
                "host": "localhost",
                "port": 27017,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            }
        },
        "data_paths": {
            "stock_dividends": "s3a://test-bucket/stock_dividends/dt={partition_date}",
            "holdings_snapshot": "s3a://test-bucket/holdings_snapshot/dt={partition_date}",
            "global_stock_transactions": "s3a://test-bucket/snapshots/global_stock_transactions/dt={partition_date}",
            "crypto_currency_transactions": "s3a://test-bucket/snapshots/crypto_currency_transactions/dt={partition_date}",
            "options_contract_transactions": "s3a://test-bucket/snapshots/options_contract_transactions/dt={partition_date}",
            "crypto_currency_pocket_transactions": "s3a://test-bucket/snapshots/crypto_currency_pocket_transactions/dt={partition_date}"
        }
    }


@pytest.fixture
def mock_pluang_plus_member_config():
    """Mock configuration for PluangPlusMember."""
    return {
        "bucket_path": "s3a://test-bucket",
        "offset": 0,
        "execution_time": "gss_post_market_close",
        "t_1": "2025-01-15",
        "t_2": "2025-01-14",
        "pluang_plus": {
            "file_bucket": "pluang-plus-bucket",
            "add_plus_file": "add_plus_members",
            "remove_plus_file": "remove_plus_members"
        },
        "pluang_plus_member_validity": {
            "plus_intermediate_file": "plus_intermediate",
            "whitelist_plus_member_file": "plus_whitelist",
            "invested_value_threshold": 1000000,
            "grace_period": 7,
            "max_records_per_file": 1000
        },
        "accounts_snapshot_folder": "accounts_snapshot",
        "user_tag_mappings_snapshot_folder": "user_tag_mappings",
        "plus_member_tag_id": 123,
        "pluang_partner_id": 1,
        "pluang_plus_member_validity_snapshot": "plus_member_snapshot",
        "cashin_snapshot_folder": "cashin_snapshot",
        "cashouts_snapshot_folder": "cashouts_snapshot",
        "fund_transactions_snapshot_folder": "fund_transactions",
        "gold_gift_snapshot": "gold_gift",
        "gold_withdrawal_snapshot": "gold_withdrawal",
        "crypto_currency_wallet_transfers_de_dupe": "crypto_transfers",
        "forex_top_ups_dedupe": "forex_topups",
        "forex_cash_outs_dedupe": "forex_cashouts",
        "stock_index_folder": "stock_index",
        "global_stock_options_accounts_folder": "global_stock_options",
        "crypto_future_snapshot_folder": "crypto_futures",
        "data_store": {
            "reporting_mongo": {
                "host": "localhost",
                "port": 27017,
                "database": "test_db",
                "username": "test_user",
                "password": "test_pass"
            },
            "invested_amount": {
                "collection": "invested_amount_collection"
            }
        }
    }


@pytest.fixture
def mock_crypto_staking_config():
    """Mock configuration for crypto staking jobs."""
    return {
        "bucket_path": "s3a://test-bucket",
        "offset": 0,
        "execution_time": "jkt_day_end",
        "t_1": "2025-01-15",
        "t_2": "2025-01-14",
        "crypto_staking_topic_actor_prop": "crypto_staking_actor",
        "crypto_currency_price_api": "https://api.test.com/price",
        "kafka_topics": {
            "staking_user_transactions_topic": "staking_user_transactions",
            "crypto_net_staking_requests": "crypto_net_staking_requests",
            "staking_user_accrued_rewards_topic": "staking_user_accrued_rewards",
            "staking_user_disbursed_rewards_topic": "staking_user_disbursed_rewards"
        }
    }


@pytest.fixture
def mock_spark_utils():
    """Mock SparkUtils."""
    spark_utils = Mock()
    spark_utils.create_spark_session = Mock()
    spark_utils.stop_spark = Mock()
    return spark_utils


@pytest.fixture
def mock_io_utils():
    """Mock IOUtils."""
    io_utils = Mock()
    io_utils.read_parquet_data = Mock()
    io_utils.read_csv_file = Mock()
    io_utils.read_from_kafka_in_memory = Mock()
    io_utils.write_parquet_file = Mock()
    io_utils.write_csv_file = Mock()
    io_utils.write_dataset_to_mongo = Mock()
    io_utils.get_mongo_connection_string = Mock(return_value="*******************************************")
    return io_utils


@pytest.fixture
def mock_operations():
    """Mock Operations."""
    ops = Mock()
    return ops


@pytest.fixture
def mock_date_utils():
    """Mock DateUtils."""
    with patch('src.jobs.trading_competition.flash_games_pnl.DateUtils') as mock_date_utils:
        mock_date_utils.get_utc_timestamp_from_string.return_value = datetime(2025, 1, 15, 10, 0, 0)
        mock_date_utils.get_utc_timestamp.return_value = datetime(2025, 1, 15, 14, 0, 0)
        mock_date_utils.get_tc_dates_and_timestamp.return_value = (
            datetime(2025, 1, 15).date(),  # t_1
            14,  # h_1
            datetime(2025, 1, 14).date(),  # t_2
            14,  # h_2
            "2025-01-15T14:00:00.000Z",  # dt_1
            "2025-01-14T17:00:00.000Z"  # dt_2
        )
        yield mock_date_utils


@pytest.fixture
def sample_transactions_data(spark_session):
    """Sample transaction data for testing."""
    data = [
        (1001, 101, 1, 1, 0.0, "global_stocks", 1001,
         datetime(2025, 1, 15, 11, 0, 0), datetime(2025, 1, 15, 11, 0, 0),
         100.0, 50.0, "BUY", 15000.0, datetime(2025, 1, 15, 11, 0, 0),
         "global_stock_transactions", 52.0, 15100),
        (1001, 101, 1, 1, 5.0, "global_stocks", 1002,
         datetime(2025, 1, 15, 12, 0, 0), datetime(2025, 1, 15, 12, 0, 0),
         50.0, 55.0, "SELL", 15200.0, datetime(2025, 1, 15, 12, 0, 0),
         "global_stock_transactions", 55.0, 15200),
        (1002, 102, 2, 1, 0.0, "crypto_currency", 1003,
         datetime(2025, 1, 15, 13, 0, 0), datetime(2025, 1, 15, 13, 0, 0),
         1000.0, 0.001, "BUY", 1.0, datetime(2025, 1, 15, 13, 0, 0),
         "crypto_currency_transactions", 0.0011, 1)
    ]

    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("asset_id", LongType(), True),
        StructField("leverage", LongType(), True),
        StructField("fees", DoubleType(), True),
        StructField("asset_type", StringType(), True),
        StructField("transaction_id", LongType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("updated_executed_quantity", DoubleType(), True),
        StructField("updated_executed_unit_price", DoubleType(), True),
        StructField("transaction_type", StringType(), True),
        StructField("currency_to_idr", DoubleType(), True),
        StructField("transaction_time", TimestampType(), True),
        StructField("asset_sub_type", StringType(), True),
        StructField("current_unit_price", DoubleType(), True),
        StructField("current_currency_to_idr", LongType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_asset_data(spark_session):
    """Sample asset data for testing."""
    data = [
        ("global_stocks", 1),
        ("global_stocks", 2),
        ("crypto_currency", 101),
        ("crypto_currency", 102)
    ]

    schema = StructType([
        StructField("asset_type", StringType(), True),
        StructField("asset_id", LongType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_options_data(spark_session):
    """Sample options contracts data."""
    data = [
        (1001, 1),
        (1002, 2)
    ]

    schema = StructType([
        StructField("id", LongType(), True),
        StructField("global_stock_id", LongType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_leverage_stocks_data(spark_session):
    """Sample leverage stocks data."""
    data = [
        ({"id": 1, "status": "ACTIVE", "stock_type": "CFD_LEVERAGE"},),
        ({"id": 2, "status": "ACTIVE", "stock_type": "CFD_LEVERAGE"},),
        ({"id": 3, "status": "INACTIVE", "stock_type": "CFD_LEVERAGE"},)
    ]

    schema = StructType([
        StructField("value", MapType(StringType(), StringType()), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_niv_data(spark_session):
    """Sample NIV data with user details."""
    data = [
        (1001, 1000000.0, 2001, "John Doe", "<EMAIL>", "TC_2025_Q1"),
        (1002, 500000.0, 2002, "Jane Smith", "<EMAIL>", "TC_2025_Q1")
    ]

    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("invested_value", DoubleType(), True),
        StructField("user_id", LongType(), True),
        StructField("name", StringType(), True),
        StructField("email", StringType(), True),
        StructField("trading_competition_id", StringType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_gtv_data(spark_session):
    """Sample GTV data."""
    data = [
        (1001, 1200000.0),
        (1002, 600000.0)
    ]

    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("total_gtv", DoubleType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_user_details_data(spark_session):
    """Sample user details data."""
    data = [
        (1001, 101, "John Doe", "<EMAIL>", "TC_2025_Q1"),
        (1002, 102, "Jane Smith", "<EMAIL>", "TC_2025_Q1")
    ]

    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("name", StringType(), True),
        StructField("email", StringType(), True),
        StructField("trading_competition_id", StringType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_staking_requests_data(spark_session):
    """Sample staking requests data for testing."""
    data = [
        (1, 1001, 101, 1, 1, 825, 100.0, "STAKING_REQUESTED", "ACTIVE",
         datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 15, 10, 0, 0),
         datetime(2025, 1, 16, 3, 0, 0)),  # next_transition_time tomorrow in UTC (16th 3AM UTC = 16th 10AM JKT)
        (2, 1002, 102, 1, 1, 826, 200.0, "UNSTAKING_IN_WAITING", "ACTIVE",
         datetime(2025, 1, 14, 15, 0, 0), datetime(2025, 1, 14, 15, 0, 0),
         datetime(2025, 1, 15, 8, 0, 0)),  # next_transition_time today in UTC (15th 8AM UTC = 15th 3PM JKT)
        (3, 1003, 103, 1, 1, 825, 50.0, "STAKED", "ACTIVE",
         datetime(2025, 1, 13, 12, 0, 0), datetime(2025, 1, 13, 12, 0, 0),
         None),  # terminal status, null next_transition_time
        (4, 1004, 104, 1, 1, 827, 75.0, "STAKING_IN_PROGRESS", "ACTIVE",
         datetime(2025, 1, 14, 8, 0, 0), datetime(2025, 1, 14, 8, 0, 0),
         datetime(2025, 1, 15, 1, 0, 0))  # next_transition_time today in UTC (15th 1AM UTC = 15th 8AM JKT)
    ]

    schema = StructType([
        StructField("id", LongType(), True),
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("client_id", LongType(), True),
        StructField("partner_id", LongType(), True),
        StructField("crypto_currency_id", LongType(), True),
        StructField("quantity", DoubleType(), True),
        StructField("system_status", StringType(), True),
        StructField("user_status", StringType(), True),
        StructField("created", TimestampType(), True),
        StructField("updated", TimestampType(), True),
        StructField("next_transition_time", TimestampType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_staking_accounts_data(spark_session):
    """Sample staking accounts data for testing."""
    data = [
        (1, 1001, 101, 1, 1, 825, 100.0, 10.0),  # User 1, BTC
        (2, 1002, 102, 1, 1, 826, 200.0, 0.0),  # User 2, ETH
        (3, 1003, 103, 1, 1, 825, 50.0, 5.0),  # User 3, BTC
        (4, 1004, 104, 1, 1, 827, 0.0, 0.0),  # User 4, zero balance
        (5, 1005, 105, 1, 1, 828, -10.0, 0.0)  # User 5, negative balance
    ]

    schema = StructType([
        StructField("id", LongType(), True),
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("client_id", LongType(), True),
        StructField("partner_id", LongType(), True),
        StructField("crypto_currency_id", LongType(), True),
        StructField("staked_balance", DoubleType(), True),
        StructField("unstaking_requested_balance", DoubleType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_staking_assets_data(spark_session):
    """Sample staking assets data for testing."""
    data = [
        (825, '{"interval": "WEEKLY"}', "0 0 * * 1", "CALENDAR"),  # BTC - Weekly Monday
        (826, '{"interval": "MONTHLY"}', "0 0 1 * *", "CALENDAR"),  # ETH - Monthly 1st
        (827, '{"interval": "WEEKLY"}', "0 0 * * 0", "ROLLING"),  # ADA - Weekly Sunday, Rolling
        (828, '{"interval": "MONTHLY"}', "0 0 15 * *", "ROLLING")  # DOT - Monthly 15th, Rolling
    ]

    schema = StructType([
        StructField("crypto_currency_id", LongType(), True),
        StructField("reward_disbursal_frequency", StringType(), True),
        StructField("reward_cron_schedule", StringType(), True),
        StructField("reward_disbursal_window", StringType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_staking_accrued_rewards_data(spark_session):
    """Sample staking accrued rewards data for testing."""
    data = [
        (1, 1001, 101, 1, 1, 825, "1001_825_20250115", 100.0, 0.5,
         datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 14, 12, 0, 0)),
        (2, 1002, 102, 1, 1, 826, "1002_826_20250115", 200.0, 1.0,
         datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 14, 14, 0, 0)),
        (3, 1003, 103, 1, 1, 825, "1003_825_20250115", 50.0, 0.25,
         datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 13, 16, 0, 0)),
        (4, 1001, 101, 1, 1, 826, "1001_826_20250115", 75.0, 0.375,
         datetime(2025, 1, 15, 10, 0, 0), datetime(2025, 1, 14, 18, 0, 0))
    ]

    schema = StructType([
        StructField("id", LongType(), True),
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("client_id", LongType(), True),
        StructField("partner_id", LongType(), True),
        StructField("crypto_currency_id", LongType(), True),
        StructField("ref_id", StringType(), True),
        StructField("eligible_staked_quantity", DoubleType(), True),
        StructField("gross_reward_quantity", DoubleType(), True),
        StructField("reward_trigger_time", TimestampType(), True),
        StructField("created", TimestampType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_crypto_assets_data(spark_session):
    """Sample crypto assets data for testing."""
    data = [
        (825, "BTC"),
        (826, "ETH"),
        (827, "ADA"),
        (828, "DOT")
    ]

    schema = StructType([
        StructField("id", LongType(), True),
        StructField("symbol", StringType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def sample_rewards_disbursal_history_data(spark_session):
    """Sample rewards disbursal history data for testing."""
    data = [
        (1001, 825, datetime(2025, 1, 10).date()),  # Last disbursal 5 days ago
        (1002, 826, datetime(2025, 1, 12).date()),  # Last disbursal 3 days ago
    ]

    schema = StructType([
        StructField("account_id", LongType(), True),
        StructField("crypto_currency_id", LongType(), True),
        StructField("last_reward_disbursal_date", DateType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


@pytest.fixture
def mock_asset_utils():
    """Mock AssetUtils for crypto staking tests."""
    asset_utils = Mock()
    # Mock crypto assets data
    mock_crypto_df = Mock()
    mock_crypto_df.select.return_value = mock_crypto_df
    asset_utils.get_crypto_assets.return_value = mock_crypto_df
    return asset_utils


@pytest.fixture
def mock_requests_response():
    """Mock requests response for crypto price API."""
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "data": {
            "sellPrice": "50000.00"
        }
    }
    return mock_response


@pytest.fixture
def sample_staking_disbursed_rewards_data(spark_session):
    """Sample staking disbursed rewards data for testing."""
    data = [
        (1, 1001, 101, 1, 1, 825, "1001_825_20250120", 100.0, 0.5, 50000.0, 25000.0,
         "WEEKLY", 1, 2, datetime(2025, 1, 19, 12, 0, 0), datetime(2025, 1, 20, 8, 0, 0),
         datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 20, 10, 0, 0)),
        (2, 1002, 102, 1, 1, 826, "1002_826_20250120", 200.0, 1.0, 3000.0, 3000.0,
         "WEEKLY", 3, 4, datetime(2025, 1, 19, 14, 0, 0), datetime(2025, 1, 20, 8, 0, 0),
         datetime(2025, 1, 20, 10, 0, 0), datetime(2025, 1, 20, 10, 0, 0)),
    ]

    schema = StructType([
        StructField("id", LongType(), True),
        StructField("account_id", LongType(), True),
        StructField("user_id", LongType(), True),
        StructField("client_id", LongType(), True),
        StructField("partner_id", LongType(), True),
        StructField("crypto_currency_id", LongType(), True),
        StructField("ref_id", StringType(), True),
        StructField("eligible_staked_quantity", DoubleType(), True),
        StructField("gross_reward_quantity", DoubleType(), True),
        StructField("unit_price", DoubleType(), True),
        StructField("gross_reward_value", DoubleType(), True),
        StructField("reward_frequency", StringType(), True),
        StructField("first_accrued_reward_id", LongType(), True),
        StructField("last_accrued_reward_id", LongType(), True),
        StructField("first_accrued_reward_timestamp", TimestampType(), True),
        StructField("last_accrued_reward_timestamp", TimestampType(), True),
        StructField("reward_disbursal_time", TimestampType(), True),
        StructField("created", TimestampType(), True)
    ])

    return spark_session.createDataFrame(data, schema)


