{"raw_data_folder": "raw_data", "deleted_data_date_folder": "1970-01-01", "execution_time": "jkt_day_end", "crypto_futures_exchange_settlement_asset_id": 10000, "gold_maintenance_fees": {"months": 6, "admin_id": 1002, "part_file_batch_size": 10000}, "transactions_snapshots": {"groups": {"crypto_currency": ["crypto_currency_transactions", "crypto_currency_wallet_transfers", "crypto_future_transactions", "crypto_currency_pocket_transactions", "crypto_future_funding_transactions"], "invested_amount_calculation": ["crypto_future_transactions", "fund_transactions", "indo_stock_wallet_activity_v2"], "gold": ["gold_transactions", "gold_loans", "installment_payments", "gold_withdrawals", "gold_gift_transactions"], "global_stocks": ["options_contract_transactions", "global_stock_transactions", "global_stock_dividend_transactions", "options_ca_affected_contracts", "options_ca_user_position_adjustments", "options_corporate_actions", "global_stock_dividends_v2"], "forex": ["forex_transactions", "forex_top_ups", "forex_cash_outs", "forex_yield_transactions", "forex_unhedged_transactions"], "cash_transactions": ["topups", "cashouts", "cashbacks"], "fund": ["fund_transactions"], "indo_stocks_v2": ["indo_stock_transactions_v2", "indo_stock_wallet_activity_v2", "indo_stock_cashouts_v2", "indo_stock_dividend_transactions_v2"], "crypto_staking_daily": ["crypto_currency_staking_transactions", "crypto_currency_staking_accounts", "crypto_currency_staking_assets", "crypto_currency_staking_accrued_rewards"]}, "snapshot_config": {"crypto_currency_wallet_transfers": {"de_dupe_on": "updated_at"}, "indo_stock_wallet_activity_v2": {"de_dupe_on": "created_at"}, "options_contract_transactions": {"allow_delete": true}, "topups": {"raw_data_folder": "cashin"}}, "partitions": {"crypto_currency_transactions": 40, "crypto_currency_wallet_transfers": 10, "gold_withdrawals": 2, "gold_gift_transactions": 2, "gold_transactions": 10, "gold_loans": 3, "installment_payments": 3, "crypto_currency_pocket_transactions": 5, "crypto_future_transactions": 3, "global_stock_transactions": 10, "forex_transactions": 3, "topups": 10, "cashouts": 10}}, "cashin_snapshot_folder": "cashin_snapshot", "cashouts_snapshot_folder": "cashouts_snapshot", "stock_index_folder": "stock_index_decommission/snapshots", "global_stock_options_accounts_folder": "global_stock_options_accounts", "accounts_snapshot_folder": "accounts/snapshots", "gold_gift_snapshot": "gold_gift_transactions", "gold_withdrawal_snapshot": "gold_withdrawals", "crypto_currency_wallet_transfers_de_dupe": "crypto_currency_wallet_transfers", "forex_top_ups_dedupe": "forex_top_ups", "forex_cash_outs_dedupe": "forex_cash_outs", "pluang_plus_member_validity_snapshot": "pluang_plus_member_validity/snapshot", "user_tag_mappings_snapshot_folder": "user_tag_mappings", "crypto_future_snapshot_folder": "snapshots/crypto_future_transactions", "fund_transactions_snapshot_folder": "snapshots/fund_transactions", "pluang_plus_member_validity": {"plus_intermediate_file": "pluang_plus_member_validity/plus_intermediate", "whitelist_plus_member_file": "pluang_plus_member_validity/whitelisted_plus_member/", "invested_value_threshold": *********, "grace_period": 7, "max_records_per_file": 1000}, "accounts_and_returns_snapshots": {"groups": {"global_stocks": ["ph_global_stock_accounts", "ph_global_stock_returns"], "indo_stocks_v2": ["indo_stock_accounts_v2", "indo_stock_returns_v2", "indo_stock_wallet_v2"]}, "snapshot_config": {"indo_stock_accounts_v2": {"snapshot_folder": "indo_stock_accounts_v2/t_2_files"}, "indo_stock_returns_v2": {"snapshot_folder": "indo_stock_returns_v2/t_2_files"}, "indo_stock_wallet_v2": {"snapshot_folder": "indo_stock_wallet_v2/t_2_files", "de_dupe_on": "updated_at"}}, "partitions": {}}, "pre_define_watch_list": {"indo_stocks_v2": {"indo_stock_one_day_ohlc_price_stats_folder": "indo_stock_one_day_ohlc_price_stats", "indo_stock_corporate_action_folder": "corporate_action", "volume_ratio": 2, "limit_for_top_dividend_stocks": 20}}, "cohorting": {"individual_cohort_output_path": "cohorting/cohort_data/{cohort_id}/dt={date}/", "clevertap_cohort_upload_path": "cohorting/clevertap_cohort_upload/", "clevertap_cohort_clear_path": "cohorting/clevertap_cohort_clear/latest/", "cohort_matrix_output_path": "cohorting/cohorts/dt={date}/hr={hour}/", "top_page_visit_assets_num": 1, "page_visit_view_count_min": 5, "clevertap_max_char_limit": 512}, "gss_free_fees": {"capping_free_fees_start": "2024-10-30", "mission_period": 30, "write_free_fee": "gss_free_fee_waiver"}}